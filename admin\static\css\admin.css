/* 管理后台样式 */
body {
    background-color: #f8f9fa;
    min-height: 100vh;
}

.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: none;
    border-radius: 0.5rem;
}

.card-header {
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

.btn-primary {
    background-color: #3F51B5;
    border-color: #3F51B5;
}

.btn-primary:hover {
    background-color: #303F9F;
    border-color: #303F9F;
}

.bg-primary {
    background-color: #3F51B5 !important;
}

.text-primary {
    color: #3F51B5 !important;
}

.badge {
    font-weight: 500;
}

/* 表单样式 */
.form-control:focus, .form-select:focus {
    border-color: #3F51B5;
    box-shadow: 0 0 0 0.25rem rgba(63, 81, 181, 0.25);
}

/* 动画效果 */
.card, .btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
}
