<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}错误 - AI2Six{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body class="bg-secondary min-h-screen flex flex-col">
    <header class="bg-gradient-1 shadow-lg">
        <nav class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <a href="/" class="text-3xl font-bold text-on-dark flex items-center ai-glow">
                    <span class="mr-2">AI2Six</span>
                    <span class="text-xl text-on-dark opacity-90">AI太厉害了</span>
                </a>
                <div class="hidden md:flex space-x-6">
                    <a href="/" class="text-on-dark hover:text-accent-light transition-colors duration-300">首页</a>
                    <a href="#" class="text-on-dark hover:text-accent-light transition-colors duration-300">所有应用</a>
                    <a href="#" class="text-on-dark hover:text-accent-light transition-colors duration-300">关于我们</a>
                </div>
                <div class="md:hidden">
                    <button class="text-on-dark focus:outline-none">
                        <i class="fas fa-bars text-2xl"></i>
                    </button>
                </div>
            </div>
        </nav>
    </header>

    <main class="container mx-auto px-6 py-12 flex-grow flex items-center justify-center">
        <div class="ai-card p-8 max-w-2xl w-full text-center">
            <div class="mb-8">
                {% block error_icon %}
                <div class="w-32 h-32 bg-gradient-2 rounded-full flex items-center justify-center mx-auto">
                    <i class="fas fa-exclamation-triangle text-on-dark text-4xl"></i>
                </div>
                {% endblock %}
            </div>

            <h1 class="text-4xl font-bold mb-4 ai-text-gradient">
                {% block error_title %}出错了{% endblock %}
            </h1>

            <div class="text-light text-lg mb-8">
                {% block error_message %}
                抱歉，发生了一些错误。
                {% endblock %}
            </div>

            <div class="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 justify-center">
                <a href="/" class="btn btn-gradient">
                    <i class="fas fa-home mr-2"></i>返回首页
                </a>
                {% block additional_buttons %}{% endblock %}
            </div>
        </div>
    </main>

    {% include 'includes/footer.html' %}

    <script>
        // 简单的移动端菜单切换
        document.addEventListener('DOMContentLoaded', function() {
            const menuButton = document.querySelector('header button');
            const mobileMenu = document.createElement('div');
            mobileMenu.className = 'md:hidden bg-gradient-1 fixed inset-0 z-50 flex flex-col items-center justify-center transform transition-transform duration-300 translate-x-full';
            mobileMenu.innerHTML = `
                <a href="/" class="text-on-dark text-xl py-4">首页</a>
                <a href="#" class="text-on-dark text-xl py-4">所有应用</a>
                <a href="#" class="text-on-dark text-xl py-4">关于我们</a>
                <button class="absolute top-4 right-4 text-on-dark">
                    <i class="fas fa-times text-2xl"></i>
                </button>
            `;
            document.body.appendChild(mobileMenu);

            const closeButton = mobileMenu.querySelector('button');

            menuButton.addEventListener('click', function() {
                mobileMenu.classList.remove('translate-x-full');
            });

            closeButton.addEventListener('click', function() {
                mobileMenu.classList.add('translate-x-full');
            });

            // 处理返回上一页按钮
            setupBackButton();
        });

        // 检查上一页是否属于同一域名，并设置返回按钮行为
        function setupBackButton() {
            const backButton = document.getElementById('back-button');
            if (backButton) {
                backButton.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 检查是否有上一页记录
                    if (document.referrer) {
                        try {
                            // 获取当前域名和上一页域名
                            const currentDomain = window.location.hostname;
                            const referrerUrl = new URL(document.referrer);
                            const referrerDomain = referrerUrl.hostname;

                            // 如果域名相同，则返回上一页
                            if (currentDomain === referrerDomain) {
                                window.history.back();
                            } else {
                                // 如果域名不同，则返回首页
                                window.location.href = '/';
                            }
                        } catch (error) {
                            // 如果解析URL出错，则返回首页
                            console.error('解析上一页URL时出错:', error);
                            window.location.href = '/';
                        }
                    } else {
                        // 如果没有上一页记录，则返回首页
                        window.location.href = '/';
                    }
                });
            }
        }
    </script>
</body>
</html>
