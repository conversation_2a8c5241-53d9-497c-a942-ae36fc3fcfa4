/* 自定义动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(50px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-50px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    60% {
        transform: translateY(-10px);
    }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite ease-in-out;
}

.animate-float {
    animation: float 3s infinite ease-in-out;
}

.animate-slide-right {
    animation: slideInRight 0.5s ease-out;
}

.animate-slide-left {
    animation: slideInLeft 0.5s ease-out;
}

.animate-bounce {
    animation: bounce 2s ease-in-out;
}

/* 延迟动画 */
.delay-100 {
    animation-delay: 0.1s;
}

.delay-200 {
    animation-delay: 0.2s;
}

.delay-300 {
    animation-delay: 0.3s;
}

.delay-400 {
    animation-delay: 0.4s;
}

.delay-500 {
    animation-delay: 0.5s;
}

/* 颜色变量样式 - AI主题 */
:root {
    --primary-color: #3F51B5;    /* 靛蓝色 - 代表AI的智能和稳定 */
    --primary-dark: #303F9F;     /* 深靛蓝色 */
    --primary-light: #C5CAE9;    /* 浅靛蓝色 */
    --secondary-color: #F5F7FF;  /* 非常浅的蓝紫色背景 */
    --accent-color: #00BCD4;     /* 亮青色 - 代表AI的创新和活力 */
    --accent-dark: #0097A7;      /* 深青色 */
    --accent-light: #B2EBF2;     /* 浅青色 */
    --highlight: #7C4DFF;        /* 亮紫色 - 用于强调和突出 */
    --highlight-dark: #651FFF;   /* 深紫色 */
    --text-dark: #263238;        /* 深蓝灰色文本 */
    --text-light: #607D8B;       /* 蓝灰色文本 */
    --text-on-dark: #FFFFFF;     /* 深色背景上的文本 */
    --success: #00C853;          /* 成功色 - 绿色 */
    --info: #2196F3;             /* 信息色 - 蓝色 */
    --warning: #FFD600;          /* 警告色 - 黄色 */
    --danger: #F44336;           /* 危险色 - 红色 */
    --ai-gradient-1: linear-gradient(135deg, #3F51B5, #7C4DFF); /* AI主题渐变1 */
    --ai-gradient-2: linear-gradient(135deg, #00BCD4, #3F51B5); /* AI主题渐变2 */
    --ai-gradient-3: linear-gradient(135deg, #7C4DFF, #00BCD4); /* AI主题渐变3 */
}

/* 应用颜色类 */
.bg-primary {
    background-color: var(--primary-color);
}

.bg-primary-dark {
    background-color: var(--primary-dark);
}

.bg-primary-light {
    background-color: var(--primary-light);
}

.bg-secondary {
    background-color: var(--secondary-color);
}

.bg-accent {
    background-color: var(--accent-color);
}

.bg-accent-dark {
    background-color: var(--accent-dark);
}

.bg-accent-light {
    background-color: var(--accent-light);
}

.bg-highlight {
    background-color: var(--highlight);
}

.bg-highlight-dark {
    background-color: var(--highlight-dark);
}

.bg-success {
    background-color: var(--success);
}

.bg-info {
    background-color: var(--info);
}

.bg-warning {
    background-color: var(--warning);
}

.bg-danger {
    background-color: var(--danger);
}

.bg-gradient-1 {
    background: var(--ai-gradient-1);
}

.bg-gradient-2 {
    background: var(--ai-gradient-2);
}

.bg-gradient-3 {
    background: var(--ai-gradient-3);
}

.text-primary {
    color: var(--primary-color);
}

.text-primary-dark {
    color: var(--primary-dark);
}

.text-primary-light {
    color: var(--primary-light);
}

.text-secondary {
    color: var(--secondary-color);
}

.text-accent {
    color: var(--accent-color);
}

.text-accent-dark {
    color: var(--accent-dark);
}

.text-accent-light {
    color: var(--accent-light);
}

.text-highlight {
    color: var(--highlight);
}

.text-highlight-dark {
    color: var(--highlight-dark);
}

.text-dark {
    color: var(--text-dark);
}

.text-light {
    color: var(--text-light);
}

.text-on-dark {
    color: var(--text-on-dark);
}

.text-success {
    color: var(--success);
}

.text-info {
    color: var(--info);
}

.text-warning {
    color: var(--warning);
}

.text-danger {
    color: var(--danger);
}

/* AI主题特有样式 */
.ai-glow {
    position: relative;
}

.ai-glow::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: var(--ai-gradient-1);
    z-index: -1;
    filter: blur(15px);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: inherit;
}

.ai-glow:hover::before {
    opacity: 0.7;
}

.ai-text-gradient {
    background: var(--ai-gradient-3);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
}

.ai-border {
    position: relative;
    border: 2px solid transparent;
    background-clip: padding-box;
}

.ai-border::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
    margin: -2px;
    border-radius: inherit;
    background: var(--ai-gradient-2);
}

.ai-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.ai-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

/* 卡片悬停效果 */
.app-card {
    transition: all 0.3s ease;
    border-top: 3px solid transparent;
    position: relative;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px;
}

.app-card:hover {
    transform: translateY(-5px);
    border-top: 3px solid var(--highlight);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.app-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(63, 81, 181, 0.05), rgba(0, 188, 212, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.app-card:hover::after {
    opacity: 1;
}

.app-card img {
    transition: all 0.3s ease;
}

.app-card:hover img {
    transform: scale(1.1);
}

/* 按钮效果 */
.btn {
    display: inline-block;
    padding: 0.75rem 2rem;
    border-radius: 9999px;
    font-weight: bold;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: -1;
}

.btn:hover::before {
    transform: translateX(0);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* 脉冲下载按钮 */
@keyframes pulse-download {
    0% {
        box-shadow: 0 0 0 0 rgba(124, 77, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(124, 77, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(124, 77, 255, 0);
    }
}

.pulse-download {
    animation: pulse-download 2s infinite;
    position: relative;
}

.pulse-download:hover {
    animation: none;
    transform: scale(1.05) translateY(-2px);
}

/* 当前平台和非当前平台样式 */
@keyframes highlight-platform {
    0% {
        transform: translateY(0);
        box-shadow: 0 0 20px rgba(124, 77, 255, 0.5);
    }
    50% {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(124, 77, 255, 0.7);
    }
    100% {
        transform: translateY(0);
        box-shadow: 0 0 20px rgba(124, 77, 255, 0.5);
    }
}

.current-platform {
    border: 2px solid var(--highlight);
    box-shadow: 0 0 20px rgba(124, 77, 255, 0.5);
    transform: scale(1.05);
    z-index: 10;
    animation: highlight-platform 3s infinite ease-in-out;
}

.current-platform .btn {
    animation: pulse-download 2s infinite;
    background: var(--ai-gradient-3);
    font-size: 1.1em;
}

.current-platform::before {
    content: '推荐下载';
    position: absolute;
    top: -10px;
    right: 10px;
    background: var(--highlight);
    color: white;
    padding: 2px 10px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
    z-index: 20;
}

.non-current-platform {
    opacity: 0.8;
    filter: grayscale(20%);
    transform: scale(0.98);
}

.non-current-platform:hover {
    opacity: 1;
    filter: grayscale(0%);
    transform: translateY(-5px);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-accent {
    background-color: var(--accent-color);
    color: white;
}

.btn-accent:hover {
    background-color: var(--accent-dark);
}

.btn-highlight {
    background-color: var(--highlight);
    color: white;
}

.btn-highlight:hover {
    background-color: var(--highlight-dark);
}

.btn-gradient {
    background: var(--ai-gradient-1);
    color: white;
    border: none;
}

.btn-gradient:hover {
    background: var(--ai-gradient-3);
    box-shadow: 0 5px 15px rgba(124, 77, 255, 0.3);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--secondary-color);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-color);
}

/* 平台下载按钮样式 */
.current-platform-btn {
    transform: scale(1.05);
    font-weight: bold;
    box-shadow: 0 0 15px rgba(124, 77, 255, 0.5);
    background: var(--ai-gradient-3);
    border-color: var(--highlight);
    position: relative;
}

.current-platform-btn::after {
    content: '推荐';
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--highlight);
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: bold;
}

.non-current-platform-btn {
    opacity: 0.8;
    transform: scale(0.95);
}

.non-current-platform-btn:hover {
    opacity: 1;
    transform: scale(1);
}

/* 响应式调整 */
@media (max-width: 640px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* 在移动设备上调整下载按钮布局 */
    .flex.space-x-2.justify-between {
        flex-direction: column;
        width: 100%;
        gap: 0.5rem;
    }

    .flex.space-x-2.justify-between > a,
    .flex.space-x-2.justify-between > span {
        margin-left: 0 !important; /* 覆盖space-x-2的左边距 */
        width: 100%;
        text-align: center;
    }
}