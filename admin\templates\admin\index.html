{% extends "admin/base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>应用管理</h1>
    <div class="d-flex gap-2">
        <a href="{{ url_for('admin.new_app') }}" class="btn btn-primary">
            <i class="bi bi-plus-lg"></i> 新建应用
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>图标</th>
                        <th>名称</th>
                        <th>别名</th>
                        <th>描述</th>
                        <th>平台</th>
                        <th>iOS状态</th>
                        <th>显示状态</th>
                        <th>显示顺序</th>
                        <th>更新时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for app in apps %}
                    <tr>
                        <td>{{ app.id }}</td>
                        <td>
                            <img src="{{ url_for('static', filename='images/' + app.icon) }}" alt="{{ app.name }}" width="40" height="40" class="rounded">
                        </td>
                        <td>{{ app.name }}</td>
                        <td>{{ app.alias }}</td>
                        <td>{{ app.description }}</td>
                        <td>
                            {% for platform in app.platforms %}
                            <span class="badge bg-info">{{ platform.name }}</span>
                            {% endfor %}
                        </td>
                        <td>
                            {% if app.ios_status == 'pending' %}
                            <span class="badge bg-warning">审核中</span>
                            {% else %}
                            <span class="badge bg-success">已上架</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if app.is_display %}
                            <span class="badge bg-success">显示</span>
                            {% else %}
                            <span class="badge bg-secondary">隐藏</span>
                            {% endif %}
                        </td>
                        <td>
                            <form method="post" action="{{ url_for('admin.update_app_order', app_id=app.id) }}" class="d-flex align-items-center">
                                {{ form.hidden_tag() if form else '' }}
                                <input type="number" name="display_order" value="{{ app.display_order }}" class="form-control form-control-sm" style="width: 70px;">
                                <button type="submit" class="btn btn-sm btn-outline-primary ms-1" title="保存顺序">
                                    <i class="bi bi-check"></i>
                                </button>
                            </form>
                        </td>
                        <td>{{ app.updated_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>
                            <div class="btn-group">
                                <a href="{{ url_for('admin.edit_app', id=app.id) }}" class="btn btn-sm btn-outline-primary" title="编辑应用">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="{{ url_for('admin.app_versions', app_id=app.id) }}" class="btn btn-sm btn-outline-info" title="版本管理">
                                    <i class="bi bi-tags"></i>
                                </a>
                                <a href="{{ url_for('admin.app_screenshots', app_id=app.id) }}" class="btn btn-sm btn-outline-success" title="截图管理">
                                    <i class="bi bi-images"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ app.id }}" title="删除应用">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>

                            <!-- 删除确认模态框 -->
                            <div class="modal fade" id="deleteModal{{ app.id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">确认删除</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            确定要删除应用 <strong>{{ app.name }}</strong> 吗？此操作不可撤销。
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                            <form action="{{ url_for('admin.delete_app', id=app.id) }}" method="post">
                                                <button type="submit" class="btn btn-danger">删除</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="10" class="text-center">暂无应用</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
