<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ app.name }} - 下载 - AI2Six</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body class="bg-secondary min-h-screen flex flex-col">
    <header class="bg-gradient-1 shadow-lg">
        <nav class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <a href="/" class="text-3xl font-bold text-on-dark flex items-center ai-glow">
                    <span class="mr-2">AI2Six</span>
                    <span class="text-xl text-on-dark opacity-90">AI太厉害了</span>
                </a>
                <div class="hidden md:flex space-x-6">
                    <a href="/" class="text-on-dark hover:text-accent-light transition-colors duration-300">首页</a>
                    <a href="#" class="text-on-dark hover:text-accent-light transition-colors duration-300">所有应用</a>
                    <a href="#" class="text-on-dark hover:text-accent-light transition-colors duration-300">关于我们</a>
                </div>
                <div class="md:hidden">
                    <button class="text-on-dark focus:outline-none">
                        <i class="fas fa-bars text-2xl"></i>
                    </button>
                </div>
            </div>
        </nav>
    </header>

    <main class="container mx-auto px-6 py-12 flex-grow">
        <div class="max-w-4xl mx-auto">
            <div class="ai-card">
                <div class="bg-gradient-2 p-8 text-center rounded-t-lg">
                    <div class="relative inline-block mb-6">
                        <div class="absolute inset-0 bg-white bg-opacity-10 rounded-xl blur-md transform rotate-6"></div>
                        <a href="/app/{{ app.alias }}" class="inline-block relative z-10">
                            <img src="{{ url_for('static', filename='images/' + app.icon) }}" alt="{{ app.name }}" class="w-32 h-32 mx-auto rounded-xl shadow-lg animate-float">
                        </a>
                    </div>
                    <h1 class="text-3xl font-bold text-on-dark mb-2 ai-text-gradient">{{ app.name }}</h1>
                    <p class="text-xl text-on-dark text-opacity-90 mb-4">{{ app.description }}</p>
                    <div class="flex flex-wrap justify-center gap-2 mb-4">
                        {% for platform in app.platforms %}
                        <span class="bg-white bg-opacity-20 text-on-dark px-3 py-1 rounded-full text-sm backdrop-filter backdrop-blur-sm">{{ platform }}</span>
                        {% endfor %}
                    </div>
                </div>

                <div class="p-8">
                    <div class="mb-12">
                        <h2 class="text-2xl font-bold mb-8 text-center ai-text-gradient">选择AI智能应用下载平台</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {% if 'Android' in app.platforms %}
                            <div class="ai-card p-6 ai-glow">
                                <div class="flex items-center mb-4">
                                    <div class="w-12 h-12 bg-gradient-3 rounded-full flex items-center justify-center mr-4">
                                        <i class="fab fa-android text-on-dark text-2xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-bold text-dark text-lg">Android 版本</h3>
                                        <p class="text-light text-sm">适用于所有 Android 5.0+ 设备</p>
                                    </div>
                                </div>
                                <a href="#" class="block w-full btn btn-gradient">
                                    <i class="fas fa-download mr-2"></i>下载 APK
                                </a>
                                <div class="mt-4 text-center">
                                    <a href="#" class="inline-flex items-center text-primary hover:text-primary-dark">
                                        <i class="fab fa-google-play mr-2"></i>
                                        <span>Google Play</span>
                                    </a>
                                </div>
                            </div>
                            {% endif %}

                            {% if 'iOS' in app.platforms %}
                            <div class="ai-card p-6 ai-glow">
                                <div class="flex items-center mb-4">
                                    <div class="w-12 h-12 bg-gradient-1 rounded-full flex items-center justify-center mr-4">
                                        <i class="fab fa-apple text-on-dark text-2xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-bold text-dark text-lg">iOS 版本</h3>
                                        <p class="text-light text-sm">适用于 iPhone 和 iPad</p>
                                    </div>
                                </div>
                                {% if app.ios_status == 'pending' %}
                                <div class="bg-primary-light bg-opacity-30 p-4 rounded-lg text-center mb-2">
                                    <i class="fas fa-hourglass-half text-primary mr-2"></i>
                                    <span class="text-dark">App Store 审核中</span>
                                </div>
                                <p class="text-light text-sm text-center">该应用正在等待 App Store 审核，敬请期待！</p>
                                {% else %}
                                <a href="#" class="block w-full btn btn-gradient">
                                    <i class="fab fa-app-store-ios mr-2"></i>
                                    <span>前往 App Store</span>
                                </a>
                                {% endif %}
                            </div>
                            {% endif %}

                            {% if 'Web' in app.platforms %}
                            <div class="ai-card p-6 ai-glow">
                                <div class="flex items-center mb-4">
                                    <div class="w-12 h-12 bg-gradient-2 rounded-full flex items-center justify-center mr-4">
                                        <i class="fas fa-globe text-on-dark text-2xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-bold text-dark text-lg">Web 版本</h3>
                                        <p class="text-light text-sm">在浏览器中直接使用</p>
                                    </div>
                                </div>
                                <a href="#" class="block w-full btn btn-gradient">
                                    <i class="fas fa-external-link-alt mr-2"></i>访问网页版
                                </a>
                            </div>
                            {% endif %}

                            {% if 'Windows' in app.platforms %}
                            <div class="ai-card p-6 ai-glow">
                                <div class="flex items-center mb-4">
                                    <div class="w-12 h-12 bg-highlight rounded-full flex items-center justify-center mr-4">
                                        <i class="fab fa-windows text-on-dark text-2xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-bold text-dark text-lg">Windows版</h3>
                                        <p class="text-light text-sm">适用于 Windows 系统</p>
                                    </div>
                                </div>
                                {% if app.windows_download_url %}
                                <a href="{{ app.windows_download_url }}" class="block w-full bg-primary text-on-dark py-3 rounded text-center font-semibold transition-colors duration-300 hover:bg-primary-dark">
                                    <i class="fas fa-download mr-2"></i>下载 Windows 版
                                </a>
                                {% else %}
                                <div class="block w-full bg-gray-400 text-white py-3 rounded text-center font-semibold cursor-not-allowed">
                                    <i class="fas fa-clock mr-2"></i>即将推出
                                </div>
                                {% endif %}
                            </div>
                            {% endif %}

                            {% if 'MacOS' in app.platforms %}
                            <div class="ai-card p-6 ai-glow">
                                <div class="flex items-center mb-4">
                                    <div class="w-12 h-12 bg-highlight rounded-full flex items-center justify-center mr-4">
                                        <i class="fab fa-apple text-on-dark text-2xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-bold text-dark text-lg">MacOS版</h3>
                                        <p class="text-light text-sm">适用于 macOS 系统</p>
                                    </div>
                                </div>
                                {% if app.macos_download_url %}
                                <a href="{{ app.macos_download_url }}" class="block w-full bg-primary text-on-dark py-3 rounded text-center font-semibold transition-colors duration-300 hover:bg-primary-dark">
                                    <i class="fas fa-download mr-2"></i>下载 MacOS 版
                                </a>
                                {% else %}
                                <div class="block w-full bg-gray-400 text-white py-3 rounded text-center font-semibold cursor-not-allowed">
                                    <i class="fas fa-clock mr-2"></i>即将推出
                                </div>
                                {% endif %}
                            </div>
                            {% endif %}

                            {% if 'Linux' in app.platforms %}
                            <div class="ai-card p-6 ai-glow">
                                <div class="flex items-center mb-4">
                                    <div class="w-12 h-12 bg-highlight rounded-full flex items-center justify-center mr-4">
                                        <i class="fab fa-linux text-on-dark text-2xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-bold text-dark text-lg">Linux版</h3>
                                        <p class="text-light text-sm">适用于 Linux 系统</p>
                                    </div>
                                </div>
                                {% if app.linux_download_url %}
                                <a href="{{ app.linux_download_url }}" class="block w-full bg-primary text-on-dark py-3 rounded text-center font-semibold transition-colors duration-300 hover:bg-primary-dark">
                                    <i class="fas fa-download mr-2"></i>下载 Linux 版
                                </a>
                                {% else %}
                                <div class="block w-full bg-gray-400 text-white py-3 rounded text-center font-semibold cursor-not-allowed">
                                    <i class="fas fa-clock mr-2"></i>即将推出
                                </div>
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="border-t border-gray-200 pt-8">
                        <h2 class="text-xl font-bold mb-4 ai-text-gradient">AI应用安装指南</h2>
                        <div class="bg-primary-light bg-opacity-30 p-6 rounded-lg">
                            <ul class="list-disc pl-5 space-y-2 text-dark">
                                <li>下载完成后，请按照系统提示进行安装</li>
                                <li>Android 用户可能需要允许"未知来源"应用安装权限</li>
                                <li>iOS 用户需要信任开发者证书</li>
                                <li>如遇到安装问题，请查看<a href="#" class="text-highlight hover:underline">常见问题解答</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-8">
                <a href="/app/{{ app.alias }}" class="inline-flex items-center text-primary hover:text-highlight transition-colors duration-300">
                    <i class="fas fa-arrow-left mr-2"></i>
                    <span>返回应用详情</span>
                </a>
            </div>
        </div>
    </main>

    {% include 'includes/footer.html' %}

    <script>
        // 简单的移动端菜单切换
        document.addEventListener('DOMContentLoaded', function() {
            const menuButton = document.querySelector('button');
            const mobileMenu = document.createElement('div');
            mobileMenu.className = 'md:hidden bg-gradient-1 fixed inset-0 z-50 flex flex-col items-center justify-center transform transition-transform duration-300 translate-x-full';
            mobileMenu.innerHTML = `
                <a href="/" class="text-on-dark text-xl py-4">首页</a>
                <a href="#" class="text-on-dark text-xl py-4">所有应用</a>
                <a href="#" class="text-on-dark text-xl py-4">关于我们</a>
                <button class="absolute top-4 right-4 text-on-dark">
                    <i class="fas fa-times text-2xl"></i>
                </button>
            `;
            document.body.appendChild(mobileMenu);

            const closeButton = mobileMenu.querySelector('button');

            menuButton.addEventListener('click', function() {
                mobileMenu.classList.remove('translate-x-full');
            });

            closeButton.addEventListener('click', function() {
                mobileMenu.classList.add('translate-x-full');
            });

            // 平台检测和样式应用
            detectPlatform();
        });

        // 检测用户平台并应用相应样式
        function detectPlatform() {
            // 获取用户代理字符串
            const userAgent = navigator.userAgent.toLowerCase();

            // 检测平台
            const isAndroid = /android/.test(userAgent);
            const isIOS = /iphone|ipad|ipod/.test(userAgent);
            const isWindows = /windows/.test(userAgent);
            const isMac = /macintosh|mac os x/.test(userAgent) && !isIOS; // 排除iPad识别为Mac的情况
            const isLinux = /linux/.test(userAgent) && !isAndroid; // 排除Android识别为Linux的情况

            // 获取所有平台卡片
            let androidCard = null;
            let iosCard = null;
            let webCard = null;
            let windowsCard = null;
            let macosCard = null;
            let linuxCard = null;

            // 为所有卡片添加非当前平台样式
            const allCards = document.querySelectorAll('.ai-card.p-6.ai-glow');
            allCards.forEach(card => {
                card.classList.add('non-current-platform');

                // 根据卡片内的图标确定平台类型
                if (card.querySelector('.fa-android')) {
                    androidCard = card;
                } else if (card.querySelector('.fa-apple') && card.textContent.includes('iOS')) {
                    iosCard = card;
                } else if (card.querySelector('.fa-globe')) {
                    webCard = card;
                } else if (card.querySelector('.fa-windows')) {
                    windowsCard = card;
                } else if (card.querySelector('.fa-apple') && card.textContent.includes('MacOS')) {
                    macosCard = card;
                } else if (card.querySelector('.fa-linux')) {
                    linuxCard = card;
                }
            });

            // 根据检测结果应用当前平台样式
            if (isAndroid && androidCard) {
                androidCard.classList.remove('non-current-platform');
                androidCard.classList.add('current-platform');
            } else if (isIOS && iosCard) {
                iosCard.classList.remove('non-current-platform');
                iosCard.classList.add('current-platform');
            } else if (isWindows && windowsCard) {
                windowsCard.classList.remove('non-current-platform');
                windowsCard.classList.add('current-platform');
            } else if (isMac && macosCard) {
                macosCard.classList.remove('non-current-platform');
                macosCard.classList.add('current-platform');
            } else if (isLinux && linuxCard) {
                linuxCard.classList.remove('non-current-platform');
                linuxCard.classList.add('current-platform');
            } else if (webCard) {
                // 如果无法确定平台或者是其他平台，默认高亮Web版本
                webCard.classList.remove('non-current-platform');
                webCard.classList.add('current-platform');
            }
        }
    </script>
</body>
</html>