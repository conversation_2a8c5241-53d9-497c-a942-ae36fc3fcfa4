from flask import Flask, render_template, send_from_directory
import os

app = Flask(__name__)

# 应用列表
APPS = {
    'acgranking': {
        'name': '动漫排名',
        'alias': 'acgranking',
        'description': '快来给喜欢的动漫投票吧',
        'icon': 'acgranking.png',
        'download_url': '/download/acgranking',
        'details': '这是一款专为动漫爱好者设计的应用，用户可以为自己喜欢的动漫作品投票，查看实时排名，发现新的优质作品。支持按类型、年份筛选，还可以收藏自己喜欢的作品。',
        'features': ['实时排名', '分类筛选', '用户投票', '收藏功能', '新番推荐'],
        'platforms': ['Android', 'iOS'],
        'ios_status': 'pending'  # 'pending' 表示审核中，不提供此字段或其他值表示已上架
    },
    'fittrack': {
        'name': '健身追踪',
        'alias': 'fittrack',
        'description': '专业的健身数据记录与分析工具',
        'icon': 'fittrack.png',
        'download_url': '/download/fittrack',
        'details': '健身追踪是一款专业的健身数据记录与分析工具，帮助用户记录训练数据，分析进步趋势，制定科学的训练计划。支持多种运动类型，提供详细的数据分析和可视化图表。',
        'features': ['训练记录', '数据分析', '进度追踪', '计划制定', '营养建议'],
        'platforms': ['Android', 'iOS', 'Web'],
        # 没有ios_status字段表示已上架App Store
    },
    'recipemaster': {
        'name': '食谱大师',
        'alias': 'recipemaster',
        'description': '发现美食，分享烹饪乐趣',
        'icon': 'recipemaster.png',
        'download_url': '/download/recipemaster',
        'details': '食谱大师收集了全球各地的美食食谱，用户可以按照食材、烹饪方式、难度等多种方式筛选食谱。支持步骤详解、视频教学，还可以分享自己的创意食谱。',
        'features': ['食谱搜索', '步骤详解', '视频教学', '食材计算', '社区分享'],
        'platforms': ['Android', 'iOS', 'Web'],,
        'ios_status': 'pending'  # 审核中
    },
    'studybuddy': {
        'name': '学习伙伴',
        'alias': 'studybuddy',
        'description': '高效学习，科学规划',
        'icon': 'studybuddy.png',
        'download_url': '/download/studybuddy',
        'details': '学习伙伴是一款专为学生设计的学习辅助工具，帮助用户制定学习计划，记录学习进度，提高学习效率。支持番茄工作法，知识点复习提醒，还有各类学科的学习资源。',
        'features': ['学习计划', '番茄工作法', '知识点复习', '学习资源', '成绩追踪'],
        'platforms': ['Android', 'iOS', 'Web', 'Desktop'],
        # 没有ios_status字段表示已上架App Store
    },
    'travellog': {
        'name': '旅行日志',
        'alias': 'travellog',
        'description': '记录旅途，分享精彩',
        'icon': 'travellog.png',
        'download_url': '/download/travellog',
        'details': '旅行日志帮助用户记录旅行中的每一个精彩瞬间，支持照片、视频、文字记录，自动生成旅行路线图，还可以分享到社交媒体，与朋友分享旅行体验。',
        'features': ['旅行记录', '路线规划', '照片管理', '社交分享', '目的地推荐'],
        'platforms': ['Android', 'iOS'],
        'ios_status': 'pending'  # 审核中
    }
}

@app.route('/')
def home():
    return render_template('index.html', apps=APPS)

@app.route('/app/<alias>')
def app_details(alias):
    if alias in APPS:
        return render_template('app_details.html', app=APPS[alias])
    return "应用不存在", 404

@app.route('/download/<alias>')
def download(alias):
    if alias in APPS:
        return render_template('download.html', app=APPS[alias])
    return "应用不存在", 404

if __name__ == '__main__':
    app.run(debug=True)