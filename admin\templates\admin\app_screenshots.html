{% extends "admin/base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{{ app.name }} - 截图管理</h1>
    <div>
        <a href="{{ url_for('admin.edit_app', id=app.id) }}" class="btn btn-secondary me-2">
            <i class="bi bi-pencil"></i> 编辑应用
        </a>
        <a href="{{ url_for('admin.index') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> 返回列表
        </a>
    </div>
</div>

<!-- 上传新截图 -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">上传新截图</h5>
    </div>
    <div class="card-body">
        <form method="post" action="{{ url_for('admin.add_app_screenshot', app_id=app.id) }}" enctype="multipart/form-data">
            {{ form.hidden_tag() }}
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        {{ form.screenshot.label(class="form-label") }}
                        {{ form.screenshot(class="form-control") }}
                        {% for error in form.screenshot.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                        <div class="form-text">支持的格式: JPG, PNG, GIF</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        {{ form.platform.label(class="form-label") }}
                        {{ form.platform(class="form-control") }}
                        {% for error in form.platform.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                        <div class="form-text">选择截图适用的平台</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        {{ form.title.label(class="form-label") }}
                        {{ form.title(class="form-control") }}
                        {% for error in form.title.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control") }}
                        {% for error in form.description.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        {{ form.display_order.label(class="form-label") }}
                        {{ form.display_order(class="form-control", placeholder="数字越小越靠前，默认999") }}
                        {% for error in form.display_order.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                        <div class="form-text">数字越小越靠前显示，留空则默认为999</div>
                    </div>
                </div>
            </div>
            <div class="text-end">
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>

<!-- 截图列表 -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="mb-0">已上传截图</h5>
    </div>
    <div class="card-body">
        {% if screenshots_by_platform %}
            {% for platform_name, screenshots in screenshots_by_platform.items() %}
            <div class="mb-4">
                <h6 class="text-primary mb-3">
                    <i class="bi bi-folder"></i> {{ platform_name }}截图
                    <span class="badge bg-secondary">{{ screenshots|length }}</span>
                </h6>
                <div class="row">
                    {% for screenshot in screenshots %}
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <img src="{{ url_for('static', filename='images/screenshots/' + screenshot.filename) }}" class="card-img-top" alt="{{ screenshot.title or '应用截图' }}">
                            <div class="card-body">
                                <h5 class="card-title">{{ screenshot.title or '未命名截图' }}</h5>
                                <p class="card-text">{{ screenshot.description or '无描述' }}</p>
                                <p class="card-text">
                                    <small class="text-muted">显示顺序: {{ screenshot.display_order }}</small>
                                    {% if screenshot.platform %}
                                    <br><small class="text-info">平台: {{ screenshot.platform.name }}</small>
                                    {% else %}
                                    <br><small class="text-success">平台: 通用</small>
                                    {% endif %}
                                </p>
                            </div>
                            <div class="card-footer bg-white">
                                <form method="post" action="{{ url_for('admin.delete_app_screenshot', app_id=app.id, screenshot_id=screenshot.id) }}" onsubmit="return confirm('确定要删除这张截图吗？此操作不可恢复。');">
                                    <button type="submit" class="btn btn-sm btn-danger w-100">
                                        <i class="bi bi-trash"></i> 删除截图
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endfor %}
        {% else %}
        <div class="alert alert-info">
            <i class="bi bi-info-circle"></i> 还没有上传任何截图。
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
