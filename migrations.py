from flask import Flask
from flask_migrate import Migrate
from admin.models import db
from admin.config import Config

def create_migrate_app():
    """创建用于数据库迁移的Flask应用"""
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # 初始化数据库
    db.init_app(app)
    
    # 初始化迁移
    migrate = Migrate(app, db)
    
    return app, migrate

# 创建应用和迁移对象
app, migrate = create_migrate_app()

if __name__ == '__main__':
    # 此文件不直接运行，而是通过flask命令使用
    pass
