# 后台管理系统设置说明

## 数据库初始化

运行以下命令初始化数据库并创建默认管理员账号：

```bash
flask init-db
```

这个命令会：
1. 创建所有数据库表
2. 自动创建默认管理员账号
3. 创建默认平台（Android、iOS、Web、Windows、MacOS、Linux）

## 默认管理员账号信息

- **用户名**: `admin`
- **密码**: `ai2six2025`
- **邮箱**: `<EMAIL>`

## 登录后台管理

1. 启动应用：`python app.py`
2. 访问：`http://localhost:5007/admin/login`
3. 使用上述默认账号登录

## 注意事项

- 注册功能已被完全移除，无法通过界面注册新用户
- 如需创建新的管理员账号，请使用命令行：
  ```bash
  flask create-admin <用户名> <邮箱> <密码>
  ```
- 建议在生产环境中修改默认管理员密码

## 安全建议

1. 首次登录后立即修改默认密码
2. 定期更换管理员密码
3. 确保管理后台只能通过安全网络访问
