<footer class="bg-gradient-1 text-on-dark mt-auto">
    <div class="container mx-auto px-6 py-12">
        <div class="flex flex-col md:flex-row justify-between">
            <div class="mb-8 md:mb-0">
                <h2 class="text-2xl font-bold mb-4 ai-text-gradient">AI2Six</h2>
                <p class="text-on-dark opacity-80 max-w-md">我们致力于开发融合AI技术的高质量应用，为用户提供智能、高效的数字生活体验。</p>
            </div>
            <div class="grid grid-cols-2 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-accent-light">快速链接</h3>
                    <ul class="space-y-2">
                        <li><a href="/" class="text-on-dark opacity-80 hover:opacity-100 hover:text-accent-light">首页</a></li>
                        <li><a href="#" class="text-on-dark opacity-80 hover:opacity-100 hover:text-accent-light">所有应用</a></li>
                        <li><a href="#" class="text-on-dark opacity-80 hover:opacity-100 hover:text-accent-light">关于我们</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-accent-light">联系我们</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-on-dark opacity-80 hover:opacity-100 hover:text-accent-light">反馈建议</a></li>
                        <li><a href="#" class="text-on-dark opacity-80 hover:opacity-100 hover:text-accent-light">商务合作</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="border-t border-gray-700 mt-8 pt-8 text-center">
            <p class="text-on-dark opacity-80">&copy; 2024 AI2Six. All rights reserved.</p>
        </div>
    </div>
</footer>
