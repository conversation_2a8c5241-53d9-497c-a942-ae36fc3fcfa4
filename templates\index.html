<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI2Six - AI太厉害了</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
</head>
<body class="bg-secondary min-h-screen flex flex-col">
    <header class="bg-gradient-1 shadow-lg">
        <nav class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <a href="/" class="text-3xl font-bold text-on-dark flex items-center ai-glow">
                    <span class="mr-2">AI2Six</span>
                    <span class="text-xl text-on-dark opacity-90">AI太厉害了</span>
                </a>
                <div class="hidden md:flex space-x-6">
                    <a href="/" class="text-on-dark hover:text-accent-light transition-colors duration-300">首页</a>
                    <a href="#" class="text-on-dark hover:text-accent-light transition-colors duration-300">所有应用</a>
                    <a href="#" class="text-on-dark hover:text-accent-light transition-colors duration-300">关于我们</a>
                </div>
                <div class="md:hidden">
                    <button class="text-on-dark focus:outline-none">
                        <i class="fas fa-bars text-2xl"></i>
                    </button>
                </div>
            </div>
        </nav>
    </header>

    <section class="bg-gradient-2 py-20 relative overflow-hidden">
        <div class="absolute inset-0">
            <div class="absolute top-0 left-0 w-full h-full opacity-10">
                <!-- AI图案背景 -->
                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%">
                    <defs>
                        <pattern id="ai-pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
                            <path d="M0 20 L20 0 L40 20 L20 40 Z" fill="none" stroke="white" stroke-width="1"/>
                            <circle cx="20" cy="20" r="2" fill="white"/>
                        </pattern>
                    </defs>
                    <rect x="0" y="0" width="100%" height="100%" fill="url(#ai-pattern)"/>
                </svg>
            </div>
        </div>
        <div class="container mx-auto px-6 text-center relative z-10">
            <p class="text-xl md:text-2xl text-on-dark text-opacity-90 max-w-3xl mx-auto mb-8">开发100个APP计划</p>
            <div class="flex justify-center">
                <div class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-sm px-8 py-4 rounded-full ai-border">
                    <span class="text-on-dark font-bold">已完成：</span>
                    <span class="text-accent-light font-bold text-xl">5%</span>
                </div>
            </div>

        </div>
        <!-- 装饰性元素 -->
        <div class="absolute bottom-0 left-0 w-full overflow-hidden" style="height: 80px;">
            <svg viewBox="0 0 500 150" preserveAspectRatio="none" style="height: 100%; width: 100%;">
                <path d="M0.00,49.98 C150.00,150.00 349.20,-50.00 500.00,49.98 L500.00,150.00 L0.00,150.00 Z" style="stroke: none; fill: var(--secondary-color);"></path>
            </svg>
        </div>
    </section>

    <section id="apps" class="py-16 bg-secondary">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {% for app_id, app in apps.items() %}
                <div class="app-card ai-glow relative group">
                    <a href="/app/{{ app.alias }}" class="block">
                        <div class="p-6">
                            <div class="flex justify-center mb-6">
                                <img src="{{ url_for('static', filename='images/' + app.icon) }}" alt="{{ app.name }}" class="w-24 h-24 rounded-xl shadow-md transition-transform duration-300 group-hover:scale-105">
                            </div>
                            <h2 class="text-2xl font-bold text-primary mb-2 text-center">{{ app.name }}</h2>
                            <p class="text-light mb-6 text-center">{{ app.description }}</p>
                            <div class="flex flex-wrap justify-center gap-2 mb-4">
                                {% for platform in app.platforms %}
                                <span class="bg-primary-light text-primary-dark px-3 py-1 rounded-full text-sm font-medium">{{ platform }}</span>
                                {% endfor %}
                            </div>
                        </div>
                    </a>
                    <div class="px-6 pb-6">
                        <div class="flex flex-col space-y-3">
                            <!-- 详情按钮（文字按钮） -->
                            <a href="/app/{{ app.alias }}" class="btn btn-outline w-full text-center" title="查看详情">
                                <i class="fas fa-info-circle mr-2"></i>查看详情
                            </a>

                            <!-- 下载按钮区域 -->
                            <div class="flex space-x-2 justify-between">
                                {% if 'Android' in app.platforms %}
                                <a href="{{ app.apk_download_url or app.download_url }}" class="android-download-btn btn btn-gradient pulse-download px-4 py-2 flex-1" data-platform="android">
                                    <i class="fab fa-android mr-2"></i>安卓下载
                                </a>
                                {% endif %}

                                {% if 'iOS' in app.platforms %}
                                {% if app.ios_status == 'pending' %}
                                <span class="ios-download-btn btn btn-outline px-4 py-2 opacity-70 cursor-not-allowed flex-1" data-platform="ios">
                                    <i class="fab fa-apple mr-2"></i>审核中
                                </span>
                                {% else %}
                                <a href="{{ app.app_store_url or '#' }}" class="ios-download-btn btn btn-gradient px-4 py-2 flex-1" data-platform="ios">
                                    <i class="fab fa-apple mr-2"></i>iOS下载
                                </a>
                                {% endif %}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <!-- 平台标识 -->
                    <div class="absolute top-4 right-4 flex space-x-1">
                        {% if 'Android' in app.platforms %}
                        <span class="w-8 h-8 bg-primary-light rounded-full flex items-center justify-center" title="Android版">
                            <i class="fab fa-android text-primary"></i>
                        </span>
                        {% endif %}
                        {% if 'iOS' in app.platforms %}
                        <span class="w-8 h-8 bg-primary-light rounded-full flex items-center justify-center" title="iOS版">
                            <i class="fab fa-apple text-primary"></i>
                        </span>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </section>

    {% include 'includes/footer.html' %}

    <script>
        // 简单的移动端菜单切换
        document.addEventListener('DOMContentLoaded', function() {
            const menuButton = document.querySelector('button');
            const mobileMenu = document.createElement('div');
            mobileMenu.className = 'md:hidden bg-gradient-1 fixed inset-0 z-50 flex flex-col items-center justify-center transform transition-transform duration-300 translate-x-full';
            mobileMenu.innerHTML = `
                <a href="/" class="text-on-dark text-xl py-4">首页</a>
                <a href="#" class="text-on-dark text-xl py-4">所有应用</a>
                <a href="#" class="text-on-dark text-xl py-4">关于我们</a>
                <button class="absolute top-4 right-4 text-on-dark">
                    <i class="fas fa-times text-2xl"></i>
                </button>
            `;
            document.body.appendChild(mobileMenu);

            const closeButton = mobileMenu.querySelector('button');

            menuButton.addEventListener('click', function() {
                mobileMenu.classList.remove('translate-x-full');
            });

            closeButton.addEventListener('click', function() {
                mobileMenu.classList.add('translate-x-full');
            });

            // 检测用户平台并高亮对应下载按钮
            detectUserPlatform();
        });

        // 检测用户平台并高亮对应下载按钮
        function detectUserPlatform() {
            // 获取用户代理字符串
            const userAgent = navigator.userAgent.toLowerCase();

            // 检测平台
            const isAndroid = /android/.test(userAgent);
            const isIOS = /iphone|ipad|ipod/.test(userAgent);

            // 获取所有下载按钮
            const androidButtons = document.querySelectorAll('.android-download-btn');
            const iosButtons = document.querySelectorAll('.ios-download-btn');

            // 根据平台高亮对应按钮
            if (isAndroid) {
                // 高亮安卓下载按钮
                androidButtons.forEach(button => {
                    button.classList.add('current-platform-btn');
                    // 添加脉冲动画效果
                    button.classList.add('animate-pulse');
                });

                // 降低iOS按钮的显示优先级
                iosButtons.forEach(button => {
                    button.classList.add('non-current-platform-btn');
                });
            } else if (isIOS) {
                // 高亮iOS下载按钮
                iosButtons.forEach(button => {
                    button.classList.add('current-platform-btn');
                    // 添加脉冲动画效果，但不为"审核中"的按钮添加
                    if (!button.classList.contains('cursor-not-allowed')) {
                        button.classList.add('animate-pulse');
                    }
                });

                // 降低安卓按钮的显示优先级
                androidButtons.forEach(button => {
                    button.classList.add('non-current-platform-btn');
                });
            }
        }
    </script>
</body>
</html>