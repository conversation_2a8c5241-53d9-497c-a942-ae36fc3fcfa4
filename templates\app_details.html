<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ app.name }} - AI2Six</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body class="bg-secondary min-h-screen flex flex-col">
    <header class="bg-gradient-1 shadow-lg">
        <nav class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <a href="/" class="text-3xl font-bold text-on-dark flex items-center ai-glow">
                    <span class="mr-2">AI2Six</span>
                    <span class="text-xl text-on-dark opacity-90">AI太厉害了</span>
                </a>
                <div class="hidden md:flex space-x-6">
                    <a href="/" class="text-on-dark hover:text-accent-light transition-colors duration-300">首页</a>
                    <a href="#" class="text-on-dark hover:text-accent-light transition-colors duration-300">所有应用</a>
                    <a href="#" class="text-on-dark hover:text-accent-light transition-colors duration-300">关于我们</a>
                </div>
                <div class="md:hidden">
                    <button class="text-on-dark focus:outline-none">
                        <i class="fas fa-bars text-2xl"></i>
                    </button>
                </div>
            </div>
        </nav>
    </header>

    <main class="container mx-auto px-6 py-8 flex-grow">
        <div class="ai-card">
            <!-- 应用头部信息 -->
            <div class="bg-gradient-2 p-8 rounded-t-lg">
                <div class="flex flex-col items-center">
                    <div class="relative mb-6">
                        <div class="absolute inset-0 bg-white bg-opacity-10 rounded-xl blur-md transform rotate-6"></div>
                        <img src="{{ url_for('static', filename='images/' + app.icon) }}" alt="{{ app.name }}" class="w-32 h-32 rounded-xl shadow-lg relative z-10 animate-float">
                    </div>
                    <div class="text-center">
                        <h1 class="text-4xl font-bold text-on-dark mb-2 ai-text-gradient">{{ app.name }}</h1>
                        <p class="text-xl text-on-dark text-opacity-90 mb-4">{{ app.description }}</p>
                        <div class="flex flex-wrap justify-center gap-2 mb-4">
                            {% for platform in app.platforms %}
                            <span class="bg-white bg-opacity-20 text-on-dark px-3 py-1 rounded-full text-sm backdrop-filter backdrop-blur-sm">{{ platform }}</span>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 下载按钮区域 -->
            <div class="p-6 bg-white border-b border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-3xl mx-auto">
                    {% if 'Android' in app.platforms %}
                    <div class="ai-card p-6 ai-glow">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-gradient-3 rounded-full flex items-center justify-center mr-4">
                                <i class="fab fa-android text-on-dark text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="font-bold text-dark text-lg">Android 版本</h3>
                                <p class="text-light text-sm">适用于所有 Android 5.0+ 设备</p>
                            </div>
                        </div>
                        <a href="#" class="block w-full btn btn-gradient pulse-download">
                            <i class="fas fa-download mr-2"></i>下载 APK
                        </a>
                        <div class="mt-4 text-center">
                            <a href="#" class="inline-flex items-center text-primary hover:text-primary-dark">
                                <i class="fab fa-google-play mr-2"></i>
                                <span>Google Play</span>
                            </a>
                        </div>
                    </div>
                    {% endif %}

                    {% if 'iOS' in app.platforms %}
                    <div class="ai-card p-6 ai-glow">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-gradient-1 rounded-full flex items-center justify-center mr-4">
                                <i class="fab fa-apple text-on-dark text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="font-bold text-dark text-lg">iOS 版本</h3>
                                <p class="text-light text-sm">适用于 iPhone 和 iPad</p>
                            </div>
                        </div>
                        {% if app.ios_status == 'pending' %}
                        <div class="bg-primary-light bg-opacity-30 p-4 rounded-lg text-center mb-2">
                            <i class="fas fa-hourglass-half text-primary mr-2"></i>
                            <span class="text-dark">App Store 审核中</span>
                        </div>
                        <p class="text-light text-sm text-center">该应用正在等待 App Store 审核，敬请期待！</p>
                        {% else %}
                        <a href="#" class="block w-full btn btn-gradient pulse-download">
                            <i class="fab fa-app-store-ios mr-2"></i>
                            <span>前往 App Store</span>
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}

                    {% if 'Web' in app.platforms %}
                    <div class="ai-card p-6 ai-glow">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-gradient-2 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-globe text-on-dark text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="font-bold text-dark text-lg">Web 版本</h3>
                                <p class="text-light text-sm">在浏览器中直接使用</p>
                            </div>
                        </div>
                        <a href="#" class="block w-full btn btn-gradient pulse-download">
                            <i class="fas fa-external-link-alt mr-2"></i>访问网页版
                        </a>
                    </div>
                    {% endif %}

                    {% if 'Windows' in app.platforms %}
                    <div class="ai-card p-6 ai-glow">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-highlight rounded-full flex items-center justify-center mr-4">
                                <i class="fab fa-windows text-on-dark text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="font-bold text-dark text-lg">Windows版</h3>
                                <p class="text-light text-sm">适用于 Windows 系统</p>
                            </div>
                        </div>
                        {% if app.windows_download_url %}
                        <a href="{{ app.windows_download_url }}" class="block w-full bg-primary text-on-dark py-3 rounded text-center font-semibold transition-colors duration-300 hover:bg-primary-dark hover:scale-105">
                            <i class="fas fa-download mr-2"></i>下载 Windows 版
                        </a>
                        {% else %}
                        <div class="block w-full bg-gray-400 text-white py-3 rounded text-center font-semibold cursor-not-allowed">
                            <i class="fas fa-clock mr-2"></i>即将推出
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}

                    {% if 'MacOS' in app.platforms %}
                    <div class="ai-card p-6 ai-glow">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-highlight rounded-full flex items-center justify-center mr-4">
                                <i class="fab fa-apple text-on-dark text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="font-bold text-dark text-lg">MacOS版</h3>
                                <p class="text-light text-sm">适用于 macOS 系统</p>
                            </div>
                        </div>
                        {% if app.macos_download_url %}
                        <a href="{{ app.macos_download_url }}" class="block w-full bg-primary text-on-dark py-3 rounded text-center font-semibold transition-colors duration-300 hover:bg-primary-dark hover:scale-105">
                            <i class="fas fa-download mr-2"></i>下载 MacOS 版
                        </a>
                        {% else %}
                        <div class="block w-full bg-gray-400 text-white py-3 rounded text-center font-semibold cursor-not-allowed">
                            <i class="fas fa-clock mr-2"></i>即将推出
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}

                    {% if 'Linux' in app.platforms %}
                    <div class="ai-card p-6 ai-glow">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-highlight rounded-full flex items-center justify-center mr-4">
                                <i class="fab fa-linux text-on-dark text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="font-bold text-dark text-lg">Linux版</h3>
                                <p class="text-light text-sm">适用于 Linux 系统</p>
                            </div>
                        </div>
                        {% if app.linux_download_url %}
                        <a href="{{ app.linux_download_url }}" class="block w-full bg-primary text-on-dark py-3 rounded text-center font-semibold transition-colors duration-300 hover:bg-primary-dark hover:scale-105">
                            <i class="fas fa-download mr-2"></i>下载 Linux 版
                        </a>
                        {% else %}
                        <div class="block w-full bg-gray-400 text-white py-3 rounded text-center font-semibold cursor-not-allowed">
                            <i class="fas fa-clock mr-2"></i>即将推出
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- 应用详情 -->
            <div class="p-8">
                <!-- 版本信息 -->
                {% if app.latest_version %}
                <div class="relative mb-12">
                    <h2 class="text-2xl font-bold text-primary mb-6 inline-block ai-text-gradient">版本信息</h2>
                    <div class="ai-card p-6 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-100">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <!-- 当前版本 -->
                            <div class="text-center">
                                <div class="w-16 h-16 bg-gradient-1 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-tag text-on-dark text-2xl"></i>
                                </div>
                                <h3 class="font-bold text-dark text-lg mb-2">当前版本</h3>
                                <p class="text-primary text-xl font-bold">{{ app.latest_version.version_number }}</p>
                            </div>

                            <!-- 发布日期 -->
                            <div class="text-center">
                                <div class="w-16 h-16 bg-gradient-2 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-calendar-alt text-on-dark text-2xl"></i>
                                </div>
                                <h3 class="font-bold text-dark text-lg mb-2">发布日期</h3>
                                <p class="text-light text-lg">{{ app.latest_version.release_date }}</p>
                            </div>

                            <!-- 版本状态 -->
                            <div class="text-center">
                                <div class="w-16 h-16 bg-gradient-3 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-check-circle text-on-dark text-2xl"></i>
                                </div>
                                <h3 class="font-bold text-dark text-lg mb-2">版本状态</h3>
                                <p class="text-green-600 text-lg font-semibold">最新版本</p>
                            </div>
                        </div>

                        <!-- 更新说明 -->
                        {% if app.latest_version.release_notes %}
                        <div class="mt-6 pt-6 border-t border-blue-200">
                            <h4 class="font-bold text-dark text-lg mb-3">
                                <i class="fas fa-clipboard-list mr-2 text-primary"></i>更新说明
                            </h4>
                            <div class="bg-white p-4 rounded-lg border border-blue-100">
                                <p class="text-light leading-relaxed">{{ app.latest_version.release_notes|nl2br }}</p>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <div class="relative mb-12">
                    <h2 class="text-2xl font-bold text-primary mb-4 inline-block ai-text-gradient">应用介绍</h2>
                    <p class="text-light leading-relaxed">{{ app.details }}</p>
                </div>

                <!-- 截图展示区域 -->
                <div class="relative mb-12">
                    <h2 class="text-2xl font-bold text-primary mb-6 inline-block ai-text-gradient">应用界面展示</h2>

                    {% if app.screenshots_by_platform %}
                    <!-- 平台切换标签 -->
                    <div class="mb-6">
                        <div class="flex flex-wrap gap-2" id="platform-tabs">
                            {% if app.screenshots_by_platform.general %}
                            <button class="platform-tab px-4 py-2 rounded-lg bg-gray-200 text-gray-700 hover:bg-primary hover:text-white transition-colors duration-300" data-platform="general">
                                <i class="fas fa-globe mr-2"></i>通用
                            </button>
                            {% endif %}
                            {% for platform_name in app.screenshots_by_platform.keys() %}
                                {% if platform_name != 'general' %}
                                <button class="platform-tab px-4 py-2 rounded-lg bg-gray-200 text-gray-700 hover:bg-primary hover:text-white transition-colors duration-300" data-platform="{{ platform_name }}">
                                    {% if platform_name == 'android' %}
                                    <i class="fab fa-android mr-2"></i>Android
                                    {% elif platform_name == 'ios' %}
                                    <i class="fab fa-apple mr-2"></i>iOS
                                    {% elif platform_name == 'web' %}
                                    <i class="fas fa-globe mr-2"></i>Web
                                    {% elif platform_name == 'windows' %}
                                    <i class="fab fa-windows mr-2"></i>Windows
                                    {% elif platform_name == 'macos' %}
                                    <i class="fab fa-apple mr-2"></i>MacOS
                                    {% elif platform_name == 'linux' %}
                                    <i class="fab fa-linux mr-2"></i>Linux
                                    {% else %}
                                    <i class="fas fa-desktop mr-2"></i>{{ platform_name|title }}
                                    {% endif %}
                                </button>
                                {% endif %}
                            {% endfor %}
                        </div>
                    </div>

                    <!-- 截图容器 -->
                    <div id="screenshots-container">
                        {% for platform_name, screenshots in app.screenshots_by_platform.items() %}
                        <div class="screenshot-platform hidden" data-platform="{{ platform_name }}">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                {% for screenshot in screenshots %}
                                <div class="bg-gradient-{{ loop.index % 3 + 1 }} p-1 rounded-lg overflow-hidden shadow-lg">
                                    <div class="h-64 bg-white rounded-lg flex items-center justify-center overflow-hidden">
                                        <img data-src="{{ url_for('static', filename='images/screenshots/' + screenshot.filename) }}"
                                             alt="{{ screenshot.title or '应用截图' }}"
                                             class="screenshot-img object-contain h-full w-full opacity-0 transition-opacity duration-300"
                                             loading="lazy">
                                        <div class="loading-placeholder absolute inset-0 flex items-center justify-center bg-gray-100">
                                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                                        </div>
                                    </div>
                                    {% if screenshot.title or screenshot.description %}
                                    <div class="p-2 bg-white">
                                        {% if screenshot.title %}
                                        <h3 class="text-sm font-bold text-dark">{{ screenshot.title }}</h3>
                                        {% endif %}
                                        {% if screenshot.description %}
                                        <p class="text-xs text-light">{{ screenshot.description }}</p>
                                        {% endif %}
                                    </div>
                                    {% endif %}
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="bg-gradient-1 p-1 rounded-lg overflow-hidden shadow-lg">
                            <div class="h-64 bg-white rounded-lg flex items-center justify-center">
                                <span class="text-light">暂无截图</span>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

            </div>
        </div>
    </main>

    <!-- 返回顶部按钮 -->
    <div id="back-to-top" class="fixed bottom-8 right-8 z-50 hidden">
        <button class="w-12 h-12 bg-gradient-1 text-white rounded-full shadow-lg flex items-center justify-center hover:scale-110 transition-all duration-300">
            <i class="fas fa-arrow-up"></i>
        </button>
    </div>

    {% include 'includes/footer.html' %}

    <script>
        // 简单的移动端菜单切换
        document.addEventListener('DOMContentLoaded', function() {
            const menuButton = document.querySelector('header button');
            const mobileMenu = document.createElement('div');
            mobileMenu.className = 'md:hidden bg-gradient-1 fixed inset-0 z-50 flex flex-col items-center justify-center transform transition-transform duration-300 translate-x-full';
            mobileMenu.innerHTML = `
                <a href="/" class="text-on-dark text-xl py-4">首页</a>
                <a href="#" class="text-on-dark text-xl py-4">所有应用</a>
                <a href="#" class="text-on-dark text-xl py-4">关于我们</a>
                <button class="absolute top-4 right-4 text-on-dark">
                    <i class="fas fa-times text-2xl"></i>
                </button>
            `;
            document.body.appendChild(mobileMenu);

            const closeButton = mobileMenu.querySelector('button');

            menuButton.addEventListener('click', function() {
                mobileMenu.classList.remove('translate-x-full');
            });

            closeButton.addEventListener('click', function() {
                mobileMenu.classList.add('translate-x-full');
            });

            // 返回顶部按钮功能
            const backToTopBtn = document.getElementById('back-to-top');
            const downloadSection = document.querySelector('.p-6.bg-white.border-b');

            // 监听滚动事件
            window.addEventListener('scroll', function() {
                // 如果页面滚动超过下载区域，显示按钮
                if (window.scrollY > (downloadSection.offsetTop + downloadSection.offsetHeight)) {
                    backToTopBtn.classList.remove('hidden');
                } else {
                    backToTopBtn.classList.add('hidden');
                }
            });

            // 点击返回顶部按钮
            backToTopBtn.addEventListener('click', function() {
                // 平滑滚动到下载区域
                downloadSection.scrollIntoView({ behavior: 'smooth' });
            });

            // 平台检测和样式应用
            detectPlatform();

            // 初始化截图平台切换功能
            initScreenshotPlatforms();
        });

        // 检测用户平台并应用相应样式
        function detectPlatform() {
            // 获取用户代理字符串
            const userAgent = navigator.userAgent.toLowerCase();

            // 检测平台
            const isAndroid = /android/.test(userAgent);
            const isIOS = /iphone|ipad|ipod/.test(userAgent);
            const isWindows = /windows/.test(userAgent);
            const isMac = /macintosh|mac os x/.test(userAgent) && !isIOS; // 排除iPad识别为Mac的情况
            const isLinux = /linux/.test(userAgent) && !isAndroid; // 排除Android识别为Linux的情况

            // 获取所有平台卡片
            let androidCard = null;
            let iosCard = null;
            let webCard = null;
            let windowsCard = null;
            let macosCard = null;
            let linuxCard = null;

            // 为所有卡片添加非当前平台样式
            const allCards = document.querySelectorAll('.ai-card.p-6.ai-glow');
            allCards.forEach(card => {
                card.classList.add('non-current-platform');

                // 根据卡片内的图标确定平台类型
                if (card.querySelector('.fa-android')) {
                    androidCard = card;
                } else if (card.querySelector('.fa-apple') && card.textContent.includes('iOS')) {
                    iosCard = card;
                } else if (card.querySelector('.fa-globe')) {
                    webCard = card;
                } else if (card.querySelector('.fa-windows')) {
                    windowsCard = card;
                } else if (card.querySelector('.fa-apple') && card.textContent.includes('MacOS')) {
                    macosCard = card;
                } else if (card.querySelector('.fa-linux')) {
                    linuxCard = card;
                }
            });

            // 根据检测结果应用当前平台样式
            if (isAndroid && androidCard) {
                androidCard.classList.remove('non-current-platform');
                androidCard.classList.add('current-platform');
            } else if (isIOS && iosCard) {
                iosCard.classList.remove('non-current-platform');
                iosCard.classList.add('current-platform');
            } else if (isWindows && windowsCard) {
                windowsCard.classList.remove('non-current-platform');
                windowsCard.classList.add('current-platform');
            } else if (isMac && macosCard) {
                macosCard.classList.remove('non-current-platform');
                macosCard.classList.add('current-platform');
            } else if (isLinux && linuxCard) {
                linuxCard.classList.remove('non-current-platform');
                linuxCard.classList.add('current-platform');
            } else if (webCard) {
                // 如果无法确定平台或者是其他平台，默认高亮Web版本
                webCard.classList.remove('non-current-platform');
                webCard.classList.add('current-platform');
            }
        }

        // 初始化截图平台切换功能
        function initScreenshotPlatforms() {
            const platformTabs = document.querySelectorAll('.platform-tab');
            const screenshotPlatforms = document.querySelectorAll('.screenshot-platform');

            if (platformTabs.length === 0) return;

            // 检测用户平台并设置默认显示
            const userAgent = navigator.userAgent.toLowerCase();
            const isAndroid = /android/.test(userAgent);
            const isIOS = /iphone|ipad|ipod/.test(userAgent);
            const isWindows = /windows/.test(userAgent);
            const isMac = /macintosh|mac os x/.test(userAgent) && !isIOS;
            const isLinux = /linux/.test(userAgent) && !isAndroid;

            let defaultPlatform = 'general'; // 默认显示通用截图

            // 根据用户平台确定默认显示的截图
            if (isAndroid && document.querySelector('[data-platform="android"]')) {
                defaultPlatform = 'android';
            } else if (isIOS && document.querySelector('[data-platform="ios"]')) {
                defaultPlatform = 'ios';
            } else if (isWindows && document.querySelector('[data-platform="windows"]')) {
                defaultPlatform = 'windows';
            } else if (isMac && document.querySelector('[data-platform="macos"]')) {
                defaultPlatform = 'macos';
            } else if (isLinux && document.querySelector('[data-platform="linux"]')) {
                defaultPlatform = 'linux';
            } else if (document.querySelector('[data-platform="web"]')) {
                defaultPlatform = 'web';
            }

            // 显示默认平台的截图
            showPlatformScreenshots(defaultPlatform);

            // 为平台标签添加点击事件
            platformTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const platform = this.getAttribute('data-platform');
                    showPlatformScreenshots(platform);
                });
            });
        }

        // 显示指定平台的截图
        function showPlatformScreenshots(platform) {
            const platformTabs = document.querySelectorAll('.platform-tab');
            const screenshotPlatforms = document.querySelectorAll('.screenshot-platform');

            // 更新标签状态
            platformTabs.forEach(tab => {
                if (tab.getAttribute('data-platform') === platform) {
                    tab.classList.remove('bg-gray-200', 'text-gray-700');
                    tab.classList.add('bg-primary', 'text-white');
                } else {
                    tab.classList.remove('bg-primary', 'text-white');
                    tab.classList.add('bg-gray-200', 'text-gray-700');
                }
            });

            // 显示对应平台的截图
            screenshotPlatforms.forEach(platformDiv => {
                if (platformDiv.getAttribute('data-platform') === platform) {
                    platformDiv.classList.remove('hidden');
                    // 懒加载当前平台的截图
                    lazyLoadScreenshots(platformDiv);
                } else {
                    platformDiv.classList.add('hidden');
                }
            });
        }

        // 懒加载截图
        function lazyLoadScreenshots(platformDiv) {
            const images = platformDiv.querySelectorAll('.screenshot-img[data-src]');

            images.forEach(img => {
                if (img.getAttribute('data-src') && !img.src) {
                    const placeholder = img.nextElementSibling;

                    // 创建新的图片对象来预加载
                    const newImg = new Image();
                    newImg.onload = function() {
                        img.src = this.src;
                        img.classList.remove('opacity-0');
                        img.classList.add('opacity-100');
                        if (placeholder) {
                            placeholder.style.display = 'none';
                        }
                    };
                    newImg.onerror = function() {
                        if (placeholder) {
                            placeholder.innerHTML = '<div class="text-gray-500"><i class="fas fa-exclamation-triangle"></i><br>加载失败</div>';
                        }
                    };
                    newImg.src = img.getAttribute('data-src');
                    img.removeAttribute('data-src');
                }
            });
        }
    </script>
</body>
</html>
