from flask import Flask
from flask_login import <PERSON><PERSON><PERSON>anager
from flask_migrate import Migrate
from .config import Config
from .models import db, User

login_manager = LoginManager()
login_manager.login_view = 'admin.login'
login_manager.login_message = '请先登录以访问此页面'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# 保留create_app函数以兼容现有代码
def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    db.init_app(app)
    login_manager.init_app(app)

    # 初始化迁移
    migrate = Migrate(app, db)

    from .routes import admin_bp
    app.register_blueprint(admin_bp)

    return app
