from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, PasswordField, BooleanField, SubmitField, TextAreaField, SelectMultipleField, FileField, SelectField
from wtforms.widgets import CheckboxInput, ListWidget
from wtforms.validators import DataRequired, Length
from .models import Platform

# 自定义多选框字段
class MultiCheckboxField(SelectMultipleField):
    widget = ListWidget(prefix_label=False)
    option_widget = CheckboxInput()

class LoginForm(FlaskForm):
    username = StringField('用户名', validators=[DataRequired()])
    password = PasswordField('密码', validators=[DataRequired()])
    remember_me = BooleanField('记住我')
    submit = SubmitField('登录')



class AppForm(FlaskForm):
    name = StringField('应用名称', validators=[DataRequired(), Length(max=100)])
    alias = StringField('应用别名', validators=[DataRequired(), Length(max=100)])
    description = StringField('应用描述', validators=[DataRequired(), Length(max=255)])
    icon = FileField('应用图标')
    details = TextAreaField('应用详情', validators=[DataRequired()])
    platforms = MultiCheckboxField('支持平台', coerce=int)
    features = TextAreaField('应用功能 (每行一个)', validators=[DataRequired()])

    # 显示控制
    is_display = BooleanField('在首页显示此应用', default=True)

    # Android相关字段
    apk_download_url = StringField('APK直接下载链接', validators=[Length(max=255)])
    google_play_url = StringField('Google Play下载链接', validators=[Length(max=255)])
    google_play_status = BooleanField('Google Play应用是否在审核中')

    # iOS相关字段
    ios_status = BooleanField('iOS应用是否在审核中')
    app_store_url = StringField('App Store下载链接', validators=[Length(max=255)])

    # 桌面平台相关字段
    windows_download_url = StringField('Windows直接下载链接', validators=[Length(max=255)])
    macos_download_url = StringField('MacOS直接下载链接', validators=[Length(max=255)])
    linux_download_url = StringField('Linux直接下载链接', validators=[Length(max=255)])

    submit = SubmitField('保存')

    def __init__(self, *args, **kwargs):
        super(AppForm, self).__init__(*args, **kwargs)
        self.platforms.choices = [(p.id, p.name) for p in Platform.query.all()]

class AppVersionForm(FlaskForm):
    version_number = StringField('版本号', validators=[DataRequired(), Length(max=50)])
    release_notes = TextAreaField('版本更新说明')
    submit = SubmitField('保存版本')

class AppScreenshotForm(FlaskForm):
    screenshot = FileField('截图文件', validators=[DataRequired()])
    platform = SelectField('适用平台', coerce=int)
    title = StringField('标题', validators=[Length(max=100)])
    description = StringField('描述', validators=[Length(max=255)])
    display_order = StringField('显示顺序', validators=[Length(max=10)])
    submit = SubmitField('上传截图')

    def __init__(self, *args, **kwargs):
        super(AppScreenshotForm, self).__init__(*args, **kwargs)
        # 添加"通用"选项和所有平台选项
        self.platform.choices = [(0, '通用（所有平台）')] + [(p.id, p.name) for p in Platform.query.all()]

class AppOrderForm(FlaskForm):
    display_order = StringField('显示顺序', validators=[DataRequired()])
    submit = SubmitField('保存顺序')
