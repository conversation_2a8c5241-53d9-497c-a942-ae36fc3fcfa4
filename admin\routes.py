from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, abort
from flask_login import login_user, logout_user, login_required, current_user
from urllib.parse import urlparse
from werkzeug.utils import secure_filename
import os
import shutil
import subprocess
import signal
from datetime import datetime

from .models import db, User, App, Platform, Feature, AppVersion, AppScreenshot
from .forms import LoginForm, AppForm, AppVersionForm, AppScreenshotForm, AppOrderForm
import json

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/')
@admin_bp.route('/index')
@login_required
def index():
    apps = App.query.order_by(App.display_order, App.id).all()
    form = AppOrderForm()
    return render_template('admin/index.html', title='管理后台', apps=apps, form=form)

@admin_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('admin.index'))
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user is None or not user.check_password(form.password.data):
            flash('用户名或密码错误')
            return redirect(url_for('admin.login'))
        login_user(user, remember=form.remember_me.data)
        next_page = request.args.get('next')
        if not next_page or urlparse(next_page).netloc != '':
            next_page = url_for('admin.index')
        return redirect(next_page)
    return render_template('admin/login.html', title='登录', form=form)

@admin_bp.route('/logout')
def logout():
    logout_user()
    return redirect(url_for('admin.login'))



@admin_bp.route('/app/new', methods=['GET', 'POST'])
@login_required
def new_app():
    form = AppForm()
    if form.validate_on_submit():
        # 处理图标上传
        icon_filename = 'default.png'
        if form.icon.data:
            icon_filename = secure_filename(form.icon.data.filename)
            # 保存到管理后台的静态文件夹
            admin_icon_path = os.path.join(current_app.root_path, 'static/images', icon_filename)
            form.icon.data.save(admin_icon_path)

            # 复制到前端网站的静态文件夹
            frontend_icon_path = os.path.join(current_app.root_path, '../../static/images', icon_filename)
            try:
                shutil.copy2(admin_icon_path, frontend_icon_path)
            except Exception as e:
                flash(f'图标复制到前端失败: {str(e)}', 'warning')

        # 创建新应用
        app = App(
            name=form.name.data,
            alias=form.alias.data,
            description=form.description.data,
            icon=icon_filename,
            details=form.details.data,
            # 下载链接字段
            apk_download_url=form.apk_download_url.data,
            google_play_url=form.google_play_url.data,
            google_play_status='pending' if form.google_play_status.data else None,
            ios_status='pending' if form.ios_status.data else None,
            app_store_url=form.app_store_url.data,
            # 桌面平台下载链接字段
            windows_download_url=form.windows_download_url.data,
            macos_download_url=form.macos_download_url.data,
            linux_download_url=form.linux_download_url.data,
            # 显示控制
            is_display=form.is_display.data
        )

        # 添加平台
        for platform_id in form.platforms.data:
            platform = Platform.query.get(platform_id)
            if platform:
                app.platforms.append(platform)

        # 添加功能
        features = form.features.data.strip().split('\n')
        for feature_name in features:
            if feature_name.strip():
                feature = Feature(name=feature_name.strip(), app=app)
                db.session.add(feature)

        db.session.add(app)
        db.session.commit()
        flash('应用创建成功')
        return redirect(url_for('admin.index'))

    return render_template('admin/app_form.html', title='新建应用', form=form)

@admin_bp.route('/app/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_app(id):
    app = App.query.get_or_404(id)
    form = AppForm()

    if request.method == 'GET':
        form.name.data = app.name
        form.alias.data = app.alias
        form.description.data = app.description
        form.details.data = app.details
        form.platforms.data = [p.id for p in app.platforms]
        form.features.data = '\n'.join([f.name for f in app.features])

        # 下载链接字段
        form.apk_download_url.data = app.apk_download_url
        form.google_play_url.data = app.google_play_url
        form.google_play_status.data = True if app.google_play_status == 'pending' else False
        form.ios_status.data = True if app.ios_status == 'pending' else False
        form.app_store_url.data = app.app_store_url

        # 桌面平台下载链接字段
        form.windows_download_url.data = app.windows_download_url
        form.macos_download_url.data = app.macos_download_url
        form.linux_download_url.data = app.linux_download_url

        # 显示控制
        form.is_display.data = app.is_display

    if form.validate_on_submit():
        # 处理图标上传
        if form.icon.data:
            icon_filename = secure_filename(form.icon.data.filename)
            # 保存到管理后台的静态文件夹
            admin_icon_path = os.path.join(current_app.root_path, 'static/images', icon_filename)
            form.icon.data.save(admin_icon_path)

            # 复制到前端网站的静态文件夹
            frontend_icon_path = os.path.join(current_app.root_path, '../../static/images', icon_filename)
            try:
                shutil.copy2(admin_icon_path, frontend_icon_path)
            except Exception as e:
                flash(f'图标复制到前端失败: {str(e)}', 'warning')

            app.icon = icon_filename

        # 更新应用信息
        app.name = form.name.data
        app.alias = form.alias.data
        app.description = form.description.data
        app.details = form.details.data

        # 更新下载链接字段
        app.apk_download_url = form.apk_download_url.data
        app.google_play_url = form.google_play_url.data
        app.google_play_status = 'pending' if form.google_play_status.data else None
        app.ios_status = 'pending' if form.ios_status.data else None
        app.app_store_url = form.app_store_url.data
        # 更新桌面平台下载链接字段
        app.windows_download_url = form.windows_download_url.data
        app.macos_download_url = form.macos_download_url.data
        app.linux_download_url = form.linux_download_url.data

        # 更新显示控制
        app.is_display = form.is_display.data

        app.updated_at = datetime.now()

        # 更新平台
        app.platforms = []
        for platform_id in form.platforms.data:
            platform = Platform.query.get(platform_id)
            if platform:
                app.platforms.append(platform)

        # 更新功能
        for feature in app.features:
            db.session.delete(feature)

        features = form.features.data.strip().split('\n')
        for feature_name in features:
            if feature_name.strip():
                feature = Feature(name=feature_name.strip(), app=app)
                db.session.add(feature)

        db.session.commit()
        flash('应用更新成功')
        return redirect(url_for('admin.index'))

    return render_template('admin/app_form.html', title='编辑应用', form=form, app=app)

@admin_bp.route('/app/<int:id>/delete', methods=['POST'])
@login_required
def delete_app(id):
    app = App.query.get_or_404(id)
    db.session.delete(app)
    db.session.commit()
    flash('应用已删除')
    return redirect(url_for('admin.index'))

@admin_bp.route('/platforms', methods=['GET'])
@login_required
def platforms():
    platforms = Platform.query.all()
    return render_template('admin/platforms.html', title='平台管理', platforms=platforms)

@admin_bp.route('/platform/new', methods=['POST'])
@login_required
def new_platform():
    name = request.form.get('name')
    if name:
        platform = Platform(name=name)
        db.session.add(platform)
        db.session.commit()
        flash('平台添加成功')
    return redirect(url_for('admin.platforms'))

@admin_bp.route('/platform/<int:id>/delete', methods=['POST'])
@login_required
def delete_platform(id):
    platform = Platform.query.get_or_404(id)
    db.session.delete(platform)
    db.session.commit()
    flash('平台已删除')
    return redirect(url_for('admin.platforms'))

@admin_bp.route('/sync-data', methods=['POST'])
@login_required
def sync_data():
    """刷新应用数据（现在直接从数据库读取，不需要同步到JSON文件）"""
    # 获取应用数量，用于显示消息
    app_count = App.query.count()

    flash(f'前端将直接从数据库读取 {app_count} 个应用的最新数据')
    return redirect(url_for('admin.index'))

@admin_bp.route('/app/<int:app_id>/versions', methods=['GET'])
@login_required
def app_versions(app_id):
    """应用版本管理页面"""
    app = App.query.get_or_404(app_id)
    versions = AppVersion.query.filter_by(app_id=app_id).order_by(AppVersion.created_at.desc()).all()
    form = AppVersionForm()

    return render_template('admin/app_versions.html', title=f'{app.name} - 版本管理',
                          app=app, versions=versions, form=form)

@admin_bp.route('/app/<int:app_id>/screenshots', methods=['GET'])
@login_required
def app_screenshots(app_id):
    """应用截图管理页面"""
    app = App.query.get_or_404(app_id)

    # 按平台分组获取截图
    screenshots_by_platform = {}

    # 获取通用截图（platform_id为None）
    general_screenshots = AppScreenshot.query.filter_by(app_id=app_id, platform_id=None).order_by(AppScreenshot.display_order).all()
    if general_screenshots:
        screenshots_by_platform['通用'] = general_screenshots

    # 获取各平台的截图
    platforms = Platform.query.all()
    for platform in platforms:
        platform_screenshots = AppScreenshot.query.filter_by(app_id=app_id, platform_id=platform.id).order_by(AppScreenshot.display_order).all()
        if platform_screenshots:
            screenshots_by_platform[platform.name] = platform_screenshots

    form = AppScreenshotForm()

    return render_template('admin/app_screenshots.html', title=f'{app.name} - 截图管理',
                          app=app, screenshots_by_platform=screenshots_by_platform, form=form)

@admin_bp.route('/app/<int:app_id>/version/add', methods=['POST'])
@login_required
def add_app_version(app_id):
    """添加应用版本"""
    app = App.query.get_or_404(app_id)
    form = AppVersionForm()

    if form.validate_on_submit():
        version = AppVersion(
            app_id=app_id,
            version_number=form.version_number.data,
            release_notes=form.release_notes.data
        )

        db.session.add(version)
        db.session.commit()
        flash('版本添加成功')

    return redirect(url_for('admin.app_versions', app_id=app_id))

@admin_bp.route('/app/<int:app_id>/version/<int:version_id>/delete', methods=['POST'])
@login_required
def delete_app_version(app_id, version_id):
    """删除应用版本"""
    version = AppVersion.query.get_or_404(version_id)

    # 确保版本属于正确的应用
    if version.app_id != app_id:
        flash('无效的操作', 'danger')
        return redirect(url_for('admin.index'))

    db.session.delete(version)
    db.session.commit()
    flash('版本已删除')

    return redirect(url_for('admin.app_versions', app_id=app_id))

@admin_bp.route('/app/<int:app_id>/screenshot/add', methods=['POST'])
@login_required
def add_app_screenshot(app_id):
    """添加应用截图"""
    app = App.query.get_or_404(app_id)
    form = AppScreenshotForm()

    if form.validate_on_submit():
        # 处理截图上传
        if form.screenshot.data:
            # 生成安全的文件名
            filename = secure_filename(form.screenshot.data.filename)
            # 确保文件名唯一
            base, ext = os.path.splitext(filename)
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            filename = f"{base}_{timestamp}{ext}"

            # 保存到管理后台的静态文件夹
            screenshots_dir = os.path.join(current_app.root_path, 'static/images/screenshots')
            # 确保目录存在
            os.makedirs(screenshots_dir, exist_ok=True)

            screenshot_path = os.path.join(screenshots_dir, filename)
            form.screenshot.data.save(screenshot_path)

            # 复制到前端网站的静态文件夹
            frontend_screenshots_dir = os.path.join(current_app.root_path, '../static/images/screenshots')
            # 确保目录存在
            os.makedirs(frontend_screenshots_dir, exist_ok=True)

            frontend_screenshot_path = os.path.join(frontend_screenshots_dir, filename)
            try:
                shutil.copy2(screenshot_path, frontend_screenshot_path)
            except Exception as e:
                flash(f'截图复制到前端失败: {str(e)}', 'warning')

            # 创建截图记录
            try:
                display_order = int(form.display_order.data) if form.display_order.data else 999
            except ValueError:
                display_order = 999

            # 处理平台字段，0表示通用截图
            platform_id = form.platform.data if form.platform.data != 0 else None

            screenshot = AppScreenshot(
                app_id=app.id,
                platform_id=platform_id,
                filename=filename,
                title=form.title.data,
                description=form.description.data,
                display_order=display_order
            )

            db.session.add(screenshot)
            db.session.commit()
            flash('截图上传成功')
        else:
            flash('请选择要上传的截图文件', 'error')

    return redirect(url_for('admin.app_screenshots', app_id=app_id))

@admin_bp.route('/app/<int:app_id>/screenshot/<int:screenshot_id>/delete', methods=['POST'])
@login_required
def delete_app_screenshot(app_id, screenshot_id):
    """删除应用截图"""
    screenshot = AppScreenshot.query.get_or_404(screenshot_id)

    # 确保截图属于指定的应用
    if screenshot.app_id != app_id:
        flash('无效的操作', 'danger')
        return redirect(url_for('admin.index'))

    # 删除文件
    try:
        # 删除管理后台的文件
        admin_screenshot_path = os.path.join(current_app.root_path, 'static/images/screenshots', screenshot.filename)
        if os.path.exists(admin_screenshot_path):
            os.remove(admin_screenshot_path)

        # 删除前端的文件
        frontend_screenshot_path = os.path.join(current_app.root_path, '../static/images/screenshots', screenshot.filename)
        if os.path.exists(frontend_screenshot_path):
            os.remove(frontend_screenshot_path)
    except Exception as e:
        flash(f'删除截图文件失败: {str(e)}', 'warning')

    # 删除数据库记录
    db.session.delete(screenshot)
    db.session.commit()

    flash('截图已删除')
    return redirect(url_for('admin.app_screenshots', app_id=app_id))

@admin_bp.route('/app/<int:app_id>/order', methods=['POST'])
@login_required
def update_app_order(app_id):
    """更新应用显示顺序"""
    app = App.query.get_or_404(app_id)

    try:
        # 直接从请求表单中获取显示顺序
        display_order = request.form.get('display_order', '999')
        app.display_order = int(display_order)
        db.session.commit()
        flash('应用显示顺序已更新')
    except ValueError:
        flash('显示顺序必须是数字', 'error')

    return redirect(url_for('admin.index'))
