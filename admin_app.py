from admin import create_app, db
from admin.models import User, App, Platform, Feature, AppVersion
import click
from flask.cli import with_appcontext
from flask_migrate import Migrate

app = create_app()

# 初始化迁移
migrate = Migrate(app, db)

@app.shell_context_processor
def make_shell_context():
    return {'db': db, 'User': User, 'App': App, 'Platform': Platform, 'Feature': Feature, 'AppVersion': AppVersion, 'migrate': migrate}

@click.command('init-db')
@with_appcontext
def init_db_command():
    """初始化数据库表结构"""
    db.create_all()
    click.echo('数据库表已初始化')

@click.command('create-admin')
@click.argument('username')
@click.argument('email')
@click.argument('password')
@with_appcontext
def create_admin_command(username, email, password):
    """创建管理员账户"""
    user = User.query.filter_by(username=username).first()
    if user:
        click.echo(f'用户 {username} 已存在')
        return

    user = User(username=username, email=email, is_admin=True)
    user.set_password(password)
    db.session.add(user)
    db.session.commit()
    click.echo(f'管理员 {username} 创建成功')

@click.command('create-platforms')
@with_appcontext
def create_platforms_command():
    """创建默认平台"""
    platforms = ['Android', 'iOS', 'Web', 'Windows', 'MacOS', 'Linux']
    for name in platforms:
        platform = Platform.query.filter_by(name=name).first()
        if not platform:
            platform = Platform(name=name)
            db.session.add(platform)

    db.session.commit()
    click.echo('默认平台创建成功')

app.cli.add_command(init_db_command)
app.cli.add_command(create_admin_command)
app.cli.add_command(create_platforms_command)

# 添加迁移命令
# 注意：迁移命令由Flask-Migrate自动添加，包括：
# flask db init - 初始化迁移仓库
# flask db migrate - 创建迁移脚本
# flask db upgrade - 应用迁移到数据库
# flask db downgrade - 回滚上一次迁移

if __name__ == '__main__':
    app.run(debug=True, port=5001)
