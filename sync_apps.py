import json
import os
from admin import create_app, db
from admin.models import App, Platform, Feature

def sync_apps_to_json():
    """将数据库中的应用数据同步到JSON文件"""
    app = create_app()
    with app.app_context():
        apps = App.query.all()
        apps_dict = {}
        
        for app in apps:
            platforms = [p.name for p in app.platforms]
            features = [f.name for f in app.features]
            
            apps_dict[app.alias] = {
                'name': app.name,
                'alias': app.alias,
                'description': app.description,
                'icon': app.icon,
                'download_url': f'/download/{app.alias}',
                'details': app.details,
                'features': features,
                'platforms': platforms
            }
            
            # 添加iOS状态
            if app.ios_status == 'pending':
                apps_dict[app.alias]['ios_status'] = 'pending'
        
        # 将数据写入JSON文件
        with open('apps_data.json', 'w', encoding='utf-8') as f:
            json.dump(apps_dict, f, ensure_ascii=False, indent=4)
        
        print(f"已同步 {len(apps_dict)} 个应用到 apps_data.json")

def load_apps_from_json():
    """从JSON文件加载应用数据到Flask应用"""
    try:
        with open('apps_data.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return {}

if __name__ == '__main__':
    sync_apps_to_json()
