from flask import Flask, render_template, send_from_directory, request, redirect, url_for, abort
from markupsafe import Markup
import os
import json
import click
from flask.cli import with_appcontext

# 导入管理后台相关模块
from admin import db, login_manager
from admin.config import Config
from admin.models import User, App, Platform, Feature, AppVersion, AppScreenshot
from admin.routes import admin_bp
from flask_migrate import Migrate

# 创建应用
app = Flask(__name__,
           static_folder='static',  # 指定静态文件夹
           template_folder='templates')  # 指定模板文件夹


# 确保截图目录存在
screenshots_dir = os.path.join(app.static_folder, 'images/screenshots')
os.makedirs(screenshots_dir, exist_ok=True)
admin_screenshots_dir = os.path.join(os.path.dirname(__file__), 'admin/static/images/screenshots')
os.makedirs(admin_screenshots_dir, exist_ok=True)

# 确保即使在调试模式下也显示自定义错误页面
app.config['PROPAGATE_EXCEPTIONS'] = True
app.config['PRESERVE_CONTEXT_ON_EXCEPTION'] = False

# 添加额外的模板文件夹
from jinja2 import ChoiceLoader, FileSystemLoader
app.jinja_loader = ChoiceLoader([
    app.jinja_loader,
    FileSystemLoader(os.path.join(os.path.dirname(__file__), 'admin/templates'))
])

# 加载配置
app.config.from_object(Config)

# 初始化数据库和登录管理器
db.init_app(app)
login_manager.init_app(app)

# 初始化迁移
migrate = Migrate(app, db)

# 定义nl2br过滤器
@app.template_filter('nl2br')
def nl2br_filter(text):
    if not text:
        return ""
    return Markup(text.replace('\n', '<br>'))

# 注册管理后台蓝图
app.register_blueprint(admin_bp)

# 注册管理后台静态文件路由
@app.route('/static/css/admin.css')
def admin_css():
    return send_from_directory(os.path.join(os.path.dirname(__file__), 'admin/static/css'), 'admin.css')

@app.route('/static/images/<path:filename>')
def images(filename):
    # 先尝试从前端静态文件夹加载
    try:
        return send_from_directory(os.path.join(os.path.dirname(__file__), 'static/images'), filename)
    except:
        # 如果前端没有，尝试从后台静态文件夹加载
        return send_from_directory(os.path.join(os.path.dirname(__file__), 'admin/static/images'), filename)

@app.route('/admin/static/<path:filename>')
def admin_static(filename):
    return send_from_directory(os.path.join(os.path.dirname(__file__), 'admin/static'), filename)

# 从数据库加载应用数据
def load_apps_from_db(include_hidden=False):
    """直接从数据库加载应用数据

    Args:
        include_hidden: 是否包含设置为不显示的应用，默认为False
    """
    apps_dict = {}

    try:
        # 根据是否包含隐藏应用决定查询条件，并按照display_order排序
        if include_hidden:
            apps = App.query.order_by(App.display_order, App.id).all()
        else:
            apps = App.query.filter_by(is_display=True).order_by(App.display_order, App.id).all()

        for app in apps:
            platforms = [p.name for p in app.platforms]
            features = [f.name for f in app.features]

            # 获取最新版本
            latest_version = AppVersion.query.filter_by(app_id=app.id).order_by(AppVersion.created_at.desc()).first()

            apps_dict[app.alias] = {
                'name': app.name,
                'alias': app.alias,
                'description': app.description,
                'icon': app.icon,
                'download_url': f'/download/{app.alias}',
                'details': app.details,
                'features': features,
                'platforms': platforms,
                'apk_download_url': app.apk_download_url,
                'google_play_url': app.google_play_url,
                'app_store_url': app.app_store_url,
                'windows_download_url': app.windows_download_url,
                'macos_download_url': app.macos_download_url,
                'linux_download_url': app.linux_download_url,
                'is_display': app.is_display
            }

            # 添加最新版本信息
            if latest_version:
                apps_dict[app.alias]['latest_version'] = {
                    'version_number': latest_version.version_number,
                    'release_notes': latest_version.release_notes,
                    'release_date': latest_version.created_at.strftime('%Y-%m-%d')
                }

            # 添加状态信息
            if app.ios_status == 'pending':
                apps_dict[app.alias]['ios_status'] = 'pending'

            if app.google_play_status == 'pending':
                apps_dict[app.alias]['google_play_status'] = 'pending'

            # 添加截图信息，按平台分组
            screenshots_by_platform = {}

            # 获取通用截图
            general_screenshots = AppScreenshot.query.filter_by(app_id=app.id, platform_id=None).order_by(AppScreenshot.display_order).all()
            if general_screenshots:
                screenshots_by_platform['general'] = []
                for screenshot in general_screenshots:
                    screenshots_by_platform['general'].append({
                        'filename': screenshot.filename,
                        'title': screenshot.title,
                        'description': screenshot.description
                    })

            # 获取各平台截图
            for platform in app.platforms:
                platform_screenshots = AppScreenshot.query.filter_by(app_id=app.id, platform_id=platform.id).order_by(AppScreenshot.display_order).all()
                if platform_screenshots:
                    screenshots_by_platform[platform.name.lower()] = []
                    for screenshot in platform_screenshots:
                        screenshots_by_platform[platform.name.lower()].append({
                            'filename': screenshot.filename,
                            'title': screenshot.title,
                            'description': screenshot.description
                        })

            if screenshots_by_platform:
                apps_dict[app.alias]['screenshots_by_platform'] = screenshots_by_platform

                # 为了向后兼容，保留原有的screenshots字段（显示通用截图）
                if 'general' in screenshots_by_platform:
                    apps_dict[app.alias]['screenshots'] = screenshots_by_platform['general']

    except Exception as e:
        # 如果数据库操作失败，记录错误并返回空字典
        app.logger.error(f"从数据库加载应用数据失败: {str(e)}")
        return {}

    return apps_dict

# 从JSON文件加载应用数据（作为备份方案）
def load_apps_from_json():
    try:
        with open('apps_data.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        # 如果JSON文件不存在，返回默认应用列表
        return {
            'acgranking': {
                'name': '动漫排名',
                'alias': 'acgranking',
                'description': '快来给喜欢的动漫投票吧',
                'icon': 'acgranking.png',
                'download_url': '/download/acgranking',
                'details': '这是一款专为动漫爱好者设计的应用，用户可以为自己喜欢的动漫作品投票，查看实时排名，发现新的优质作品。支持按类型、年份筛选，还可以收藏自己喜欢的作品。',
                'features': ['实时排名', '分类筛选', '用户投票', '收藏功能', '新番推荐'],
                'platforms': ['Android', 'iOS'],
                'ios_status': 'pending'
            }
        }

# 前端路由
@app.route('/')
def home():
    # 每次访问首页时直接从数据库加载应用数据
    try:
        apps = load_apps_from_db()
        if not apps:  # 如果数据库加载失败，尝试从JSON文件加载
            apps = load_apps_from_json()
    except Exception as e:
        app.logger.error(f"加载应用数据失败: {str(e)}")
        apps = load_apps_from_json()  # 作为备份方案

    return render_template('index.html', apps=apps)

@app.route('/app/<alias>')
def app_details(alias):
    # 先查询应用是否存在及其显示状态
    try:
        app_obj = App.query.filter_by(alias=alias).first()

        # 如果应用不存在，返回404
        if not app_obj:
            abort(404)

        # 如果应用设置为不显示，重定向到主页
        if not app_obj.is_display:
            return redirect(url_for('home'))

        # 应用存在且可显示，加载详细数据并渲染模板
        apps = load_apps_from_db(include_hidden=True)
        if alias in apps:
            return render_template('app_details.html', app=apps[alias])

        # 这种情况理论上不应该发生，但为了安全起见仍然处理
        abort(404)
    except Exception as e:
        app.logger.error(f"访问应用详情页失败: {str(e)}")
        abort(500)

@app.route('/download/<alias>')
def download(alias):
    # 先查询应用是否存在及其显示状态
    try:
        app_obj = App.query.filter_by(alias=alias).first()

        # 如果应用不存在或设置为不显示，重定向到主页
        if not app_obj or not app_obj.is_display:
            return redirect(url_for('home'))

        # 应用存在且可显示，加载详细数据并渲染模板
        apps = load_apps_from_db(include_hidden=True)
        if alias in apps:
            return render_template('download.html', app=apps[alias])

        # 这种情况理论上不应该发生，但为了安全起见仍然处理
        abort(404)
    except Exception as e:
        app.logger.error(f"访问下载页面失败: {str(e)}")
        abort(500)

# 错误处理程序
@app.errorhandler(404)
def page_not_found(e):
    # 记录错误信息
    app.logger.error(f'404 错误: {request.path}')
    return render_template('404.html'), 404

@app.errorhandler(403)
def forbidden(e):
    # 记录错误信息
    app.logger.error(f'403 错误: {request.path}')
    return render_template('403.html'), 403

@app.errorhandler(500)
def internal_server_error(e):
    # 记录错误信息
    app.logger.error(f'500 错误: {str(e)}')
    return render_template('500.html'), 500

@app.errorhandler(503)
def service_unavailable(e):
    # 记录错误信息
    app.logger.error(f'503 错误: {str(e)}')
    return render_template('503.html'), 503

# 通用错误处理
@app.errorhandler(Exception)
def handle_exception(e):
    # 记录错误信息
    app.logger.error(f'未处理的异常: {str(e)}')
    # 返回通用错误页面
    return render_template('error.html'), 500

# 测试错误页面的路由
@app.route('/test-error/<int:code>')
def test_error(code):
    """用于测试各种错误页面的路由"""
    if code == 404:
        abort(404)
    elif code == 403:
        abort(403)
    elif code == 500:
        # 故意触发一个异常
        1/0
    elif code == 503:
        abort(503)
    else:
        # 默认返回通用错误页面
        return render_template('error.html')

# CLI命令
@app.shell_context_processor
def make_shell_context():
    return {'db': db, 'User': User, 'App': App, 'Platform': Platform, 'Feature': Feature, 'AppVersion': AppVersion}

@click.command('init-db')
@with_appcontext
def init_db_command():
    """初始化数据库表结构并创建默认管理员账户"""
    db.create_all()
    click.echo('数据库表已初始化')

    # 创建默认管理员账户
    admin_username = 'admin'
    admin_email = '<EMAIL>'
    admin_password = 'ai2six2025'

    # 检查是否已存在管理员账户
    existing_admin = User.query.filter_by(username=admin_username).first()
    if not existing_admin:
        admin_user = User(username=admin_username, email=admin_email, is_admin=True)
        admin_user.set_password(admin_password)
        db.session.add(admin_user)
        db.session.commit()
        click.echo(f'默认管理员账户已创建: {admin_username} / {admin_password}')
    else:
        click.echo('管理员账户已存在，跳过创建')

    # 同时创建默认平台
    platforms = ['Android', 'iOS', 'Web', 'Windows', 'MacOS', 'Linux']
    for name in platforms:
        platform = Platform.query.filter_by(name=name).first()
        if not platform:
            platform = Platform(name=name)
            db.session.add(platform)

    db.session.commit()
    click.echo('默认平台创建成功')

@click.command('create-admin')
@click.argument('username')
@click.argument('email')
@click.argument('password')
@with_appcontext
def create_admin_command(username, email, password):
    """创建管理员账户"""
    user = User.query.filter_by(username=username).first()
    if user:
        click.echo(f'用户 {username} 已存在')
        return

    user = User(username=username, email=email, is_admin=True)
    user.set_password(password)
    db.session.add(user)
    db.session.commit()
    click.echo(f'管理员 {username} 创建成功')

@click.command('create-platforms')
@with_appcontext
def create_platforms_command():
    """创建默认平台"""
    platforms = ['Android', 'iOS', 'Web', 'Windows', 'MacOS', 'Linux']
    for name in platforms:
        platform = Platform.query.filter_by(name=name).first()
        if not platform:
            platform = Platform(name=name)
            db.session.add(platform)

    db.session.commit()
    click.echo('默认平台创建成功')

@click.command('update-desktop-platforms')
@with_appcontext
def update_desktop_platforms_command():
    """将Desktop平台替换为Windows、MacOS、Linux三个独立平台"""
    # 查找Desktop平台
    desktop_platform = Platform.query.filter_by(name='Desktop').first()

    if not desktop_platform:
        click.echo('未找到Desktop平台，跳过更新')
        return

    # 创建新的桌面平台
    new_platforms = ['Windows', 'MacOS', 'Linux']
    created_platforms = []

    for name in new_platforms:
        platform = Platform.query.filter_by(name=name).first()
        if not platform:
            platform = Platform(name=name)
            db.session.add(platform)
            created_platforms.append(platform)
            click.echo(f'创建平台: {name}')
        else:
            created_platforms.append(platform)
            click.echo(f'平台已存在: {name}')

    # 提交新平台
    db.session.commit()

    # 查找所有使用Desktop平台的应用
    apps_with_desktop = App.query.filter(App.platforms.contains(desktop_platform)).all()

    for app in apps_with_desktop:
        click.echo(f'更新应用: {app.name}')
        # 移除Desktop平台
        app.platforms.remove(desktop_platform)
        # 添加新的桌面平台
        for platform in created_platforms:
            if platform not in app.platforms:
                app.platforms.append(platform)

    # 删除Desktop平台
    db.session.delete(desktop_platform)
    db.session.commit()

    click.echo('Desktop平台已成功替换为Windows、MacOS、Linux三个独立平台')

# 添加CLI命令
app.cli.add_command(init_db_command)
app.cli.add_command(create_admin_command)
app.cli.add_command(create_platforms_command)
app.cli.add_command(update_desktop_platforms_command)

# 主程序入口
if __name__ == '__main__':
    # 保存进程ID，用于后台管理系统重启前端应用
    with open('frontend.pid', 'w') as f:
        f.write(str(os.getpid()))

    # 根据环境变量决定是否启用调试模式
    debug_mode = os.environ.get('FLASK_DEBUG', 'False').lower() in ('true', '1', 't')

    # 即使在调试模式下也能显示自定义错误页面
    if debug_mode:
        app.logger.info('应用以调试模式启动')
    else:
        app.logger.info('应用以生产模式启动')

    # 启动应用
    app.run(host="0.0.0.0", debug=debug_mode, port=5007)
