{% extends "admin/base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{{ app.name }} - 版本管理</h1>
    <div class="d-flex gap-2">
        <a href="{{ url_for('admin.edit_app', id=app.id) }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> 返回应用编辑
        </a>
        <a href="{{ url_for('admin.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-list"></i> 应用列表
        </a>
    </div>
</div>

<!-- 添加新版本 -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">添加新版本</h5>
    </div>
    <div class="card-body">
        <form method="post" action="{{ url_for('admin.add_app_version', app_id=app.id) }}">
            {{ form.hidden_tag() }}
            <div class="mb-3">
                {{ form.version_number.label(class="form-label") }}
                {{ form.version_number(class="form-control") }}
                {% for error in form.version_number.errors %}
                <div class="text-danger">{{ error }}</div>
                {% endfor %}
            </div>
            <div class="mb-3">
                {{ form.release_notes.label(class="form-label") }}
                {{ form.release_notes(class="form-control", rows="3") }}
                {% for error in form.release_notes.errors %}
                <div class="text-danger">{{ error }}</div>
                {% endfor %}
            </div>
            <div class="d-grid">
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>

<!-- 版本列表 -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="mb-0">版本历史</h5>
    </div>
    <div class="card-body">
        {% if versions %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>版本号</th>
                        <th>发布时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for version in versions %}
                    <tr>
                        <td>{{ version.version_number }}</td>
                        <td>{{ version.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#versionModal{{ version.id }}">
                                    <i class="bi bi-info-circle"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteVersionModal{{ version.id }}">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>

                    <!-- 版本详情模态框 -->
                    <div class="modal fade" id="versionModal{{ version.id }}" tabindex="-1" aria-labelledby="versionModalLabel{{ version.id }}" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="versionModalLabel{{ version.id }}">版本 {{ version.version_number }} 详情</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <h6>版本更新说明:</h6>
                                    <div class="p-3 bg-light rounded mb-3">
                                        {{ version.release_notes|nl2br|default('无更新说明', true) }}
                                    </div>
                                    <p><strong>发布时间:</strong> {{ version.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 删除版本确认模态框 -->
                    <div class="modal fade" id="deleteVersionModal{{ version.id }}" tabindex="-1" aria-labelledby="deleteVersionModalLabel{{ version.id }}" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="deleteVersionModalLabel{{ version.id }}">确认删除</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <p>确定要删除版本 <strong>{{ version.version_number }}</strong> 吗？此操作不可撤销。</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                    <form action="{{ url_for('admin.delete_app_version', app_id=app.id, version_id=version.id) }}" method="post" class="d-inline">
                                        <button type="submit" class="btn btn-danger">确认删除</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i> 暂无版本记录
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
