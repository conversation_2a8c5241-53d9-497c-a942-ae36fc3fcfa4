#!/usr/bin/env python
from flask import Flask
from flask_migrate import Migrate, MigrateCommand
from flask_script import Manager
from admin.models import db
from admin.config import Config

# 创建应用
app = Flask(__name__)
app.config.from_object(Config)

# 初始化数据库
db.init_app(app)

# 初始化迁移
migrate = Migrate(app, db)

# 初始化管理器
manager = Manager(app)
manager.add_command('db', MigrateCommand)

if __name__ == '__main__':
    manager.run()
