{% extends "admin/base.html" %}

{% block styles %}
<style>
    .platform-checkboxes {
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        padding: 10px;
        margin-bottom: 10px;
        background-color: #f8f9fa;
    }

    .platform-checkboxes .form-check {
        margin-bottom: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{{ '编辑应用' if app else '新建应用' }}</h1>
    <a href="{{ url_for('admin.index') }}" class="btn btn-secondary">
        <i class="bi bi-arrow-left"></i> 返回列表
    </a>
</div>

<div class="card">
    <div class="card-body">
        <form method="post" enctype="multipart/form-data">
            {{ form.hidden_tag() }}

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control") }}
                        {% for error in form.name.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        {{ form.alias.label(class="form-label") }}
                        {{ form.alias(class="form-control") }}
                        {% for error in form.alias.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <div class="mb-3">
                {{ form.description.label(class="form-label") }}
                {{ form.description(class="form-control") }}
                {% for error in form.description.errors %}
                <div class="text-danger">{{ error }}</div>
                {% endfor %}
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        {{ form.icon.label(class="form-label") }}
                        {{ form.icon(class="form-control") }}
                        {% for error in form.icon.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                        {% if app %}
                        <div class="mt-2">
                            <p>当前图标:</p>
                            <img src="{{ url_for('static', filename='images/' + app.icon) }}" alt="{{ app.name }}" width="64" height="64" class="rounded">
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        {{ form.platforms.label(class="form-label") }}
                        <div class="platform-checkboxes">
                            {% for value, label in form.platforms.choices %}
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="platforms" value="{{ value }}" id="platform{{ value }}"
                                    {% if value|string in form.platforms.data|map('string')|list %}checked{% endif %}>
                                <label class="form-check-label" for="platform{{ value }}">
                                    {{ label }}
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                        {% for error in form.platforms.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>

                    <!-- 平台选择后的内容移到这里 -->
                </div>
            </div>

            <!-- 下载链接部分 -->
            <div class="card mb-3">
                <div class="card-header bg-light">
                    <h5 class="mb-0">下载链接设置</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Android相关设置 -->
                        <div class="col-md-6">
                            <h6 class="mb-3">Android设置</h6>
                            <div class="mb-3">
                                {{ form.apk_download_url.label(class="form-label") }}
                                {{ form.apk_download_url(class="form-control") }}
                                {% for error in form.apk_download_url.errors %}
                                <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                            <div class="mb-3">
                                {{ form.google_play_url.label(class="form-label") }}
                                {{ form.google_play_url(class="form-control") }}
                                {% for error in form.google_play_url.errors %}
                                <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                            <div class="mb-3 form-check">
                                {{ form.google_play_status(class="form-check-input") }}
                                {{ form.google_play_status.label(class="form-check-label") }}
                            </div>
                        </div>

                        <!-- iOS相关设置 -->
                        <div class="col-md-6">
                            <h6 class="mb-3">iOS设置</h6>
                            <div class="mb-3">
                                {{ form.app_store_url.label(class="form-label") }}
                                {{ form.app_store_url(class="form-control") }}
                                {% for error in form.app_store_url.errors %}
                                <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                            <div class="mb-3 form-check">
                                {{ form.ios_status(class="form-check-input") }}
                                {{ form.ios_status.label(class="form-check-label") }}
                            </div>
                        </div>
                    </div>

                    <!-- 桌面平台相关设置 -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="mb-3">桌面平台设置</h6>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                {{ form.windows_download_url.label(class="form-label") }}
                                {{ form.windows_download_url(class="form-control") }}
                                {% for error in form.windows_download_url.errors %}
                                <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                {{ form.macos_download_url.label(class="form-label") }}
                                {{ form.macos_download_url(class="form-control") }}
                                {% for error in form.macos_download_url.errors %}
                                <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                {{ form.linux_download_url.label(class="form-label") }}
                                {{ form.linux_download_url(class="form-control") }}
                                {% for error in form.linux_download_url.errors %}
                                <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                {{ form.details.label(class="form-label") }}
                {{ form.details(class="form-control", rows="5") }}
                {% for error in form.details.errors %}
                <div class="text-danger">{{ error }}</div>
                {% endfor %}
            </div>

            <div class="mb-3">
                {{ form.features.label(class="form-label") }}
                {{ form.features(class="form-control", rows="5") }}
                {% for error in form.features.errors %}
                <div class="text-danger">{{ error }}</div>
                {% endfor %}
                <div class="form-text">每行输入一个功能</div>
            </div>

            <!-- 显示控制 -->
            <div class="card mb-3">
                <div class="card-header bg-light">
                    <h5 class="mb-0">显示控制</h5>
                </div>
                <div class="card-body">
                    <div class="form-check">
                        {{ form.is_display(class="form-check-input") }}
                        {{ form.is_display.label(class="form-check-label") }}
                        <div class="form-text">取消勾选将不会在首页显示此应用，但仍可通过直接链接访问</div>
                    </div>
                </div>
            </div>


            <div class="d-grid">
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}
