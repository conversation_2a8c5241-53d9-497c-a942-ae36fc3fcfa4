from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
import hashlib
from datetime import datetime

db = SQLAlchemy()

# 应用和平台的多对多关系表
app_platform = db.Table('app_platform',
    db.Column('app_id', db.<PERSON>teger, db.<PERSON>('app.id'), primary_key=True),
    db.Column('platform_id', db.Integer, db.ForeignKey('platform.id'), primary_key=True)
)

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(32))
    is_admin = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.now)

    def set_password(self, password):
        self.password_hash = hashlib.md5(password.encode()).hexdigest()

    def check_password(self, password):
        return self.password_hash == hashlib.md5(password.encode()).hexdigest()

    def __repr__(self):
        return f'<User {self.username}>'

class Platform(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)

    def __repr__(self):
        return f'<Platform {self.name}>'

class Feature(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    app_id = db.Column(db.Integer, db.ForeignKey('app.id'), nullable=False)

    def __repr__(self):
        return f'<Feature {self.name}>'

class App(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    alias = db.Column(db.String(100), unique=True, nullable=False)
    description = db.Column(db.String(255), nullable=False)
    icon = db.Column(db.String(255), nullable=False)
    details = db.Column(db.Text, nullable=False)
    ios_status = db.Column(db.String(20), default=None)  # 'pending' 或 None
    # 新增下载链接字段
    apk_download_url = db.Column(db.String(255))
    google_play_url = db.Column(db.String(255))
    google_play_status = db.Column(db.String(20), default=None)  # 'pending' 或 None
    app_store_url = db.Column(db.String(255))
    # 桌面平台下载链接字段
    windows_download_url = db.Column(db.String(255))
    macos_download_url = db.Column(db.String(255))
    linux_download_url = db.Column(db.String(255))
    # 是否在首页显示
    is_display = db.Column(db.Boolean, default=True)
    # 显示顺序（数字越小越靠前）
    display_order = db.Column(db.Integer, default=999)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # 关系
    platforms = db.relationship('Platform', secondary=app_platform, lazy='subquery',
                               backref=db.backref('apps', lazy=True))
    features = db.relationship('Feature', backref='app', lazy=True, cascade="all, delete-orphan")
    versions = db.relationship('AppVersion', backref='app', lazy=True, cascade="all, delete-orphan")
    screenshots = db.relationship('AppScreenshot', backref='app', lazy=True, cascade="all, delete-orphan")

    def __repr__(self):
        return f'<App {self.name}>'

    @property
    def download_url(self):
        return f'/download/{self.alias}'

class AppVersion(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    app_id = db.Column(db.Integer, db.ForeignKey('app.id'), nullable=False)
    version_number = db.Column(db.String(50), nullable=False)
    release_notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.now)

    def __repr__(self):
        return f'<AppVersion {self.version_number} for App {self.app_id}>'

class AppScreenshot(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    app_id = db.Column(db.Integer, db.ForeignKey('app.id'), nullable=False)
    platform_id = db.Column(db.Integer, db.ForeignKey('platform.id'), nullable=True)  # 关联平台，可为空表示通用截图
    filename = db.Column(db.String(255), nullable=False)
    title = db.Column(db.String(100))
    description = db.Column(db.String(255))
    display_order = db.Column(db.Integer, default=999)  # 截图显示顺序
    created_at = db.Column(db.DateTime, default=datetime.now)

    # 关系
    platform = db.relationship('Platform', backref='screenshots')

    def __repr__(self):
        return f'<AppScreenshot {self.id} for App {self.app_id}>'
