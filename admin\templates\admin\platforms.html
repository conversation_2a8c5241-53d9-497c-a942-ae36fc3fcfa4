{% extends "admin/base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>平台管理</h1>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">添加平台</h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('admin.new_platform') }}" method="post">
                    <div class="input-group">
                        <input type="text" name="name" class="form-control" placeholder="平台名称" required>
                        <button type="submit" class="btn btn-primary">添加</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">平台列表</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>名称</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for platform in platforms %}
                            <tr>
                                <td>{{ platform.id }}</td>
                                <td>{{ platform.name }}</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deletePlatformModal{{ platform.id }}">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                    
                                    <!-- 删除确认模态框 -->
                                    <div class="modal fade" id="deletePlatformModal{{ platform.id }}" tabindex="-1" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">确认删除</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    确定要删除平台 <strong>{{ platform.name }}</strong> 吗？此操作不可撤销。
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                    <form action="{{ url_for('admin.delete_platform', id=platform.id) }}" method="post">
                                                        <button type="submit" class="btn btn-danger">删除</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="3" class="text-center">暂无平台</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
